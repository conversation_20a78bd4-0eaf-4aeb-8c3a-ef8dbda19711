html,
body {
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
  font-family: Roboto, 'Helvetica Neue', sans-serif;
  font-size: 14px;
}

:root {
  --primar-dropdown-bg-color: #f1f9ff;
  --primary-color: #50bf3f;
  --secondary-color: #4dbd74;
  --primary-application-color: #aad3ff;
  --secondary-application-color: #ffffff;
  --footer-icon-color: #195fac;
  --primary-text-color: #195fac;
  --secondary-text-color: #8c8c8c;
  --the-sky-blue: #d7eaff;
  --dark-sky-blue: #dfedfc;
  --table-header-text-color: #ffffff;
  --table-header-bg-color: #195fac;
  --table-even-row-bg-color: #f2f2f2;
  --table-odd-row-bg-color: #ffffff;
  --table-edge-border-radius: 8px;
  --table-border-color: #afafaf;
  --table-hover-row-bg-color: #d4f2ff;
  // --headerText-color: #25591d;
  // --tableHead-color: #25591D;
  // --shaded-secondary-color: #9dd195;
  // --property-block-bgColor: #ffffff;
  // --property-block-color: #50bf3f;
  // --property-list-header-color: #50bf3f66;
  // --primary-orange: #ea7727;
  // --primary-blue: #50a7f3;
  // --cardInfoBgColor: #2d353c;
  // --cardTitleFontColor: #242a30;
  // --cardInfoFontColor: #fff;
  // --red-color: #ea483f;
  // --green-color: #61b922;
  // --orange-color: #d08931;
  // --yellow-color: #f3dc12;
  // --magenta-color: rgba(216, 33, 134, 0.973);
  // --brown-color: #d08931;
  // --color-ash: #ccc;
  // --color-darkAsh: #dadada;
  // --primary-ash: #455463;
  // --primary-darkAsh: #242a30;
  // --primary-light-ash: #6a7179;
  // --primary-white: #fff;
  // --btn-primary-blue: #191970;
  // --btn-primary-light-blue: #50a7f3;
  // --primary-blue-color: #4180c3;
  // --primary-brightBlue: #1e4b7b;
  // --table-color-lightGrey: #d9e0e7;
  // --primary-lightColor: #e8e8e8;
  // --primary-body-lightBlue: #edf6ff;
  // --primary-white: #fff;
  // --light-blue: #92bdea;
  // --card-overlay: #4180c3cf;
  // --hover-light-blue: #92bdea2a;
  // --btn-disabled-color: #bab5b5;
  // --snackbar-success: #4caf50;
  // --snackbar-error: #f44336;
  // --snackbar-warn: #FF9800;
  // --snackbar-info: #2196f3;
  // Sidebar styles
  --sidebar-background: #aad3ff;
  --sidebar-menu-text-color: #000;
  --sidebar-menu-text-hover: #1890ff;
  --sidebar-box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.4);
  --sidebar-active-menu-item-bg: rgba(0, 106, 206, 0.1);
  --sidebar-hover-menu-item-bg: rgba(24, 144, 255, 0.1);
  --sidebar-menu-icon-color: #595959;
  --sidebar-menu-arrow-color: #8c8c8c;
  --sidebar-expanded-width: 230px;
  --sidebar-collapsed-width: 70px;
  --sidebar-toggle-btn-bg: rgba(24, 144, 255, 0.1);
  --header-container-height: 8%;

  --logo-text-color: #1890ff;
  --logo-container-border-bottom: 1px solid #ffffff;
  --avatar-bg-color: #4285f4;
  // --avatar-text-color: #595959;
  // --avatar-border-radius: 50%;
  // --avatar-size: 40px;
  // --avatar-icon-size: 20px;
  // --avatar-icon-color: #8c8c8c;
  // --avatar-icon-hover-color: #1890ff;
  // --avatar-icon-active-color: #40a9ff;
  --primary-button-color: #195fac;
  --primary-button-hover-color: #3175bd;
}

@media (max-width: 500px) {
  :root {
    // --sidebar-expanded-width: 100%;
    --sidebar-collapsed-width: 50px;
  }
}

.hidden {
  display: none;
}

.page-title {
  margin: 0;
  font-size: 25px;
  font-weight: bold;
  color: #333;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  padding: 0px 4rem;
  gap: 15px;
  height: 64px;
  background: var(--secondary-application-color);
  box-shadow: 0px 4px 4px rgba(140, 140, 140, 0.3);
}

.header-container {
  width: 100%;
  height: var(--header-container-height);
  margin: 0;
  font-family: Arial, sans-serif;
  background-color: #f5f7fa;
  display: flex;
  flex-direction: column;
  overflow: auto;
  position: sticky;
  top: 0;
  z-index: 9;
}

.pointer {
  cursor: pointer;
}

.cursor-move {
  cursor: move;
}

nz-select {
  width: 100%;
}

// And input & select box global styles
nz-input-group.ant-input-affix-wrapper,
nz-select.ant-select-single:not(.ant-select-customize-input)
  .ant-select-selector {
  align-items: center;
  border-radius: 10px !important;
  border: 1px solid #e4dada;
}
nz-select.ant-select:not(.ant-select-customize-input)
  .ant-select-selector
  .ant-select-selection-search-input {
  height: 100%;
}

// Ant button global styles
button.ant-btn {
  border-radius: 0.5rem;
  height: 40px;
  font-size: 14px;
  font-weight: 550;
  letter-spacing: 0%;
  line-height: 22px;
  text-align: center;
  padding-left: 2rem;
  padding-right: 2rem;
}

button.ant-btn-link:not([disabled]) {
  color: var(--primary-text-color) !important;
  font-weight: bold;

  & span:hover {
    text-decoration: underline !important;
  }
}

button.ant-btn-primary:not(.ant-btn-dangerous):not([disabled]) {
  border-color: var(--primary-button-color) !important;
  background-color: var(--primary-button-color) !important;

  &:hover {
    border-color: var(--primary-button-hover-color) !important;
    background-color: var(--primary-button-hover-color) !important;
  }
}

button.ant-btn-primary.ant-btn-background-ghost:not(.ant-btn-dangerous):not(
    [disabled]
  ) {
  color: var(--primary-text-color) !important;
  border-color: var(--primary-button-color) !important;
  background-color: #ffffff !important;

  &:hover {
    border-color: var(--primary-button-hover-color) !important;
    background-color: var(--primary-button-hover-color) !important;
    color: #fff !important;
  }
}

// justify-content: space-between;
//     padding-left: 5rem;
//     padding-right: 5rem;
// Ant tab global styles
nz-tabset .ant-tabs-nav-list {
  width: 100%;
  display: flex;
  justify-content: space-between;

  a {
    font-size: large;
    color: var(--secondary-text-color);
  }

  .ant-tabs-tab-active a {
    color: var(--primary-text-color);
  }
}

.ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
  color: #195fac !important;
}

.ant-drawer.ant-drawer-right .ant-drawer-content-wrapper {
  width: 30% !important;
}

::ng-deep .ant-drawer-header {
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3) !important;
}

::ng-deep
  .ant-select-single:not(.ant-select-customize-input)
  .ant-select-selector {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
}

.nz-drawer-form-field {
  height: 45px;
  border: 1px solid #d8cece;
  border-radius: 7px;
  width: 100%;
}

.nzdrawer-form-item {
  display: block !important;
}

.user-edit-form-buttons {
  display: flex;
  align-items: flex-start;
  gap: 20px;
}

::ng-deep .ant-drawer-title {
  font-size: 20px !important;
  font-weight: bold;
}

::ng-deep .ant-breadcrumb .active-breadcrumb {
  color: var(--primary-button-color);
}

.non-active-breadcrumb:hover {
  color: var(--primary-button-color);
}

::ng-deep .ant-switch-checked {
  background-color: var(--primary-button-color) !important;
}

.image-wrapper {
  position: relative;
  display: inline-block;
}

.rounded-circle {
  width: 100px !important;
  height: 100px !important;
  object-fit: cover;
}

.camera-icon {
  position: absolute;
  bottom: 20px;
  right: 10px;
  background: #ffffff;
  border-radius: 50%;
  padding: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.camera-icon i {
  color: blue;
  font-size: 20px;
}

.nz-icon-style {
  cursor: pointer;
  font-size: 22px;
  color: var(--primary-button-color);
}

.ant-select-single .ant-select-selector {
  height: 100% !important;
}

.ant-notification-notice-with-icon .ant-notification-notice-description {
  font-size: 17px !important;
}

.ant-switch-checked {
  background-color: var(--primary-button-color) !important;
}

.form-field {
  height: 45px;
  border-radius: 10px !important;
}

.ant-notification-bottomLeft .ant-notification-notice,
.ant-notification-topLeft .ant-notification-notice {
  white-space: nowrap !important;
  width: auto !important;
}

.ant-notification-notice-close {
  right: 10px !important;
}

.custom-modal .ant-modal-content {
  position: relative;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 30px;
  width: 650px;
  margin: 0 auto;
  top: 250px;
  height: 300px;
}

.custom-modal .ant-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  border-bottom: none;
}

.custom-modal .ant-modal-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.custom-modal .ant-modal-body {
  margin-bottom: 16px;
  // text-align: center;
}

.custom-modal .ant-modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  border-top: none;
}

.custom-modal .ant-btn {
  padding: 4px 15px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  font-size: 14px;
}

.custom-modal .ant-btn-default {
  background-color: #fff;
  color: #333;
  border: 1px solid #d9d9d9;
}

.custom-modal .ant-btn-primary {
  background-color: #ff4d4f;
  color: #fff;
  border: 1px solid #ff4d4f;
}

.custom-modal .ant-btn-primary:hover {
  background-color: #ff7875;
  border-color: #ff7875;
}

.custom-modal .warning-icon {
  width: 16px;
  height: 16px;
  background-color: #faad14;
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
}

.custom-modal .warning-icon::before {
  content: '!';
  color: #fff;
  font-size: 12px;
  font-weight: bold;
}

.ant-modal-confirm .ant-modal-confirm-btns {
  text-align: center !important;
}

.ant-modal-confirm .ant-modal-body {
  padding: 60px 32px 24px !important;
}

.custom-modal .ant-modal-confirm-body {
  justify-content: center;
  display: flex;
}

.active-breadcrumb {
  color: var(--primary-button-color) !important;
  cursor: pointer;
  font-weight: bold;
}

.chevron {
  margin-left: 8px;
}

