import { Routes } from '@angular/router';
import { HomepageComponent } from './features/homepage/homepage.component';
import { DashboardComponent } from './features/dashboard/dashboard.component';
import { LoginComponent } from './auth/login/login.component';

import { FavouritesComponent } from './features/favourites/favourites.component';
import { NotificationsComponent } from './features/notifications/notifications.component';
import { PricingManagementComponent } from './features/pricing-management/pricing-management.component';
import { ROUTES } from './core/constants/routes';
import { roleGuard } from './core/guards/auth.guard';
import { EnumUserRole } from './core/enumerations/user-roles';
import { MockRechargeComponent } from './core/features/recharge-mock/recharge-mock.component';

import { CartItemComponent } from './features/cart-item/cart-item.component';
import { MockPaymentComponent } from './core/features/mock-payment/mock-payment.component';

export const routes: Routes = [
  { path: '', redirectTo: ROUTES.sidebar.homepage, pathMatch: 'full' },
  { path: ROUTES.auth.login, component: LoginComponent },
  {
    path: ROUTES.sidebar.dashboard,
    component: DashboardComponent,
    canActivate: [roleGuard([EnumUserRole.PLATFORM_ADMIN])],
    data: { title: 'Dashboard View' },
  },
  {
    path: ROUTES.sidebar.homepage,
    component: HomepageComponent,
    canActivate: [
      roleGuard([
        EnumUserRole.COMPANY_ADMIN,
        EnumUserRole.COMPANY_MEMBER,
        EnumUserRole.INDEPENDEDNT_AGENT,
      ]),
    ],
    data: { title: 'Featured Searches' },
  },

  {
    path: ROUTES.sidebar.purchases,
    loadChildren: () =>
      import('./features/purchases/purchase.routes').then(
        (m) => m.TRANSACTIONS_ROUTES,
      ),
    canActivate: [
      roleGuard([
        EnumUserRole.COMPANY_ADMIN,
        EnumUserRole.COMPANY_MEMBER,
        EnumUserRole.INDEPENDEDNT_AGENT,
      ]),
    ],
    data: { title: 'My Transactions' },
  },
  {
    path: ROUTES.sidebar.company,
    loadChildren: () =>
      import('./features/company-management/company-management-routes').then(
        (m) => m.COMPANY_MANAGEMENT_ROUTES,
      ),
    data: { title: 'Company Management' },
    canActivate: [
      roleGuard([EnumUserRole.PLATFORM_ADMIN, EnumUserRole.COMPANY_ADMIN]),
    ],
  },
  {
    path: ROUTES.sidebar.favorites,
    component: FavouritesComponent,
    canActivate: [roleGuard([EnumUserRole.COMPANY_ADMIN])],
    data: { title: 'Favorites' },
  },
  {
    path: ROUTES.sidebar.notifications,
    component: NotificationsComponent,
    canActivate: [roleGuard([EnumUserRole.COMPANY_ADMIN])],
    data: { title: 'Notifications' },
  },
  {
    path: ROUTES.sidebar.pricing,
    component: PricingManagementComponent,
    data: { title: 'Pricing Management' },
    canActivate: [roleGuard([EnumUserRole.PLATFORM_ADMIN])],
  },
  {
    path: ROUTES.mockRecharge,
    component: MockRechargeComponent,
    data: { title: 'Mock Recharge' },
    canActivate: [
      roleGuard([
        EnumUserRole.COMPANY_ADMIN,
        EnumUserRole.COMPANY_MEMBER,
        EnumUserRole.INDEPENDEDNT_AGENT,
      ]),
    ],
  },
  {
    path: ROUTES.cartItem,
    component: CartItemComponent,
    data: { title: 'Cart Item' },
    canActivate: [
      roleGuard([
        EnumUserRole.COMPANY_ADMIN,
        EnumUserRole.COMPANY_MEMBER,
        EnumUserRole.INDEPENDEDNT_AGENT,
      ]),
    ],
  },
  {
    path: 'mock-payment',
    component: MockPaymentComponent,
    data: { title: 'Mock Payment' },
  },
  { path: '**', redirectTo: ROUTES.sidebar.homepage }, // Fallback route
];
