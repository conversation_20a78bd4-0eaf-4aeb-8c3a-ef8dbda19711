/**
 * Areadocs Documentation
 *
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

export interface DocumentPriceRequestDTO {
  documentTypeId?: number;
  productCode: string;
  name: string;
  description?: string;
  basePrice: number;
  effectiveBasePrice?: number;
  effectiveDate: string;
  expiryDate?: string;
  effectivePriceGst?: number;
}
