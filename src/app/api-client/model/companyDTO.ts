/**
 * Areadocs Documentation
 *
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { AddressResponseDTO } from './addressResponseDTO';

export interface CompanyDTO {
  id?: number;
  readonly createdAt?: string;
  readonly modifiedAt?: string;
  isActive?: boolean;
  readonly version?: number;
  readonly deleteAt?: string;
  name?: string;
  industry?: string;
  description?: string;
  activeUserCount?: number;
  website?: string;
  employeeCount?: number;
  accountsContactNumber?: string;
  accountsContactName?: string;
  billingEmail?: string;
  primaryAddress?: AddressResponseDTO;
  billingAddress?: AddressResponseDTO;
  totalDocumentsOrdered?: number;
  totalDocumentPrice?: number;
  active?: boolean;
  acn?: string;
  abn?: string;
}
