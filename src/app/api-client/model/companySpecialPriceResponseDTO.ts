/**
 * Areadocs Documentation
 *
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { DocumentPriceResponseDTO } from './documentPriceResponseDTO';

export interface CompanySpecialPriceResponseDTO {
  id?: number;
  readonly createdAt?: string;
  readonly modifiedAt?: string;
  readonly isActive?: boolean;
  readonly version?: number;
  readonly deleteAt?: string;
  companyId?: number;
  documentPrice?: DocumentPriceResponseDTO;
  specialPrice?: number;
  specialPriceGst?: number;
}
