/**
 * Areadocs Documentation
 *
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/* tslint:disable:no-unused-variable member-ordering */

import { Inject, Injectable, Optional } from '@angular/core';
import {
  HttpClient,
  HttpHeaders,
  HttpParams,
  HttpResponse,
  HttpEvent,
  HttpParameterCodec,
  HttpContext,
} from '@angular/common/http';
import { CustomHttpParameterCodec } from '../encoder';
import { Observable } from 'rxjs';

// @ts-ignore
import { ApiResponseDocumentPriceResponseDTO } from '../model/apiResponseDocumentPriceResponseDTO';
// @ts-ignore
import { ApiResponsePageDocumentPriceResponseDTO } from '../model/apiResponsePageDocumentPriceResponseDTO';
// @ts-ignore
import { ApiResponseVoid } from '../model/apiResponseVoid';
// @ts-ignore
import { DocumentPriceRequestDTO } from '../model/documentPriceRequestDTO';

// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS } from '../variables';
import { Configuration } from '../configuration';
import { BaseService } from '../api.base.service';

@Injectable({
  providedIn: 'root',
})
export class DocumentPriceControllerService extends BaseService {
  constructor(
    protected httpClient: HttpClient,
    @Optional() @Inject(BASE_PATH) basePath: string | string[],
    @Optional() configuration?: Configuration,
  ) {
    super(basePath, configuration);
  }

  /**
   * Create a new document price
   * Creates a new document price with the provided details. Required fields: productCodeId, basePrice, effectiveDate, gst. Optional fields: documentTypeId, effectiveBasePrice, expiryDate.
   * @param documentPriceRequestDTO
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public createDocumentPrice(
    documentPriceRequestDTO: DocumentPriceRequestDTO,
    observe?: 'body',
    reportProgress?: boolean,
    options?: {
      httpHeaderAccept?: 'application/json';
      context?: HttpContext;
      transferCache?: boolean;
    },
  ): Observable<ApiResponseDocumentPriceResponseDTO>;
  public createDocumentPrice(
    documentPriceRequestDTO: DocumentPriceRequestDTO,
    observe?: 'response',
    reportProgress?: boolean,
    options?: {
      httpHeaderAccept?: 'application/json';
      context?: HttpContext;
      transferCache?: boolean;
    },
  ): Observable<HttpResponse<ApiResponseDocumentPriceResponseDTO>>;
  public createDocumentPrice(
    documentPriceRequestDTO: DocumentPriceRequestDTO,
    observe?: 'events',
    reportProgress?: boolean,
    options?: {
      httpHeaderAccept?: 'application/json';
      context?: HttpContext;
      transferCache?: boolean;
    },
  ): Observable<HttpEvent<ApiResponseDocumentPriceResponseDTO>>;
  public createDocumentPrice(
    documentPriceRequestDTO: DocumentPriceRequestDTO,
    observe: any = 'body',
    reportProgress: boolean = false,
    options?: {
      httpHeaderAccept?: 'application/json';
      context?: HttpContext;
      transferCache?: boolean;
    },
  ): Observable<any> {
    if (
      documentPriceRequestDTO === null ||
      documentPriceRequestDTO === undefined
    ) {
      throw new Error(
        'Required parameter documentPriceRequestDTO was null or undefined when calling createDocumentPrice.',
      );
    }

    let localVarHeaders = this.defaultHeaders;

    // authentication (bearer-jwt) required
    localVarHeaders = this.configuration.addCredentialToHeaders(
      'bearer-jwt',
      'Authorization',
      localVarHeaders,
      'Bearer ',
    );

    const localVarHttpHeaderAcceptSelected: string | undefined =
      options?.httpHeaderAccept ??
      this.configuration.selectHeaderAccept(['application/json']);
    if (localVarHttpHeaderAcceptSelected !== undefined) {
      localVarHeaders = localVarHeaders.set(
        'Accept',
        localVarHttpHeaderAcceptSelected,
      );
    }

    const localVarHttpContext: HttpContext =
      options?.context ?? new HttpContext();

    const localVarTransferCache: boolean = options?.transferCache ?? true;

    // to determine the Content-Type header
    const consumes: string[] = ['application/json'];
    const httpContentTypeSelected: string | undefined =
      this.configuration.selectHeaderContentType(consumes);
    if (httpContentTypeSelected !== undefined) {
      localVarHeaders = localVarHeaders.set(
        'Content-Type',
        httpContentTypeSelected,
      );
    }

    let responseType_: 'text' | 'json' | 'blob' = 'json';
    if (localVarHttpHeaderAcceptSelected) {
      if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
        responseType_ = 'text';
      } else if (
        this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)
      ) {
        responseType_ = 'json';
      } else {
        responseType_ = 'blob';
      }
    }

    let localVarPath = `/document-price`;
    const { basePath, withCredentials } = this.configuration;
    return this.httpClient.request<ApiResponseDocumentPriceResponseDTO>(
      'post',
      `${basePath}${localVarPath}`,
      {
        context: localVarHttpContext,
        body: documentPriceRequestDTO,
        responseType: <any>responseType_,
        ...(withCredentials ? { withCredentials } : {}),
        headers: localVarHeaders,
        observe: observe,
        transferCache: localVarTransferCache,
        reportProgress: reportProgress,
      },
    );
  }

  /**
   * Delete a document price
   * Soft deletes a document price by its ID
   * @param id Document price ID
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public deleteDocumentPrice(
    id: number,
    observe?: 'body',
    reportProgress?: boolean,
    options?: {
      httpHeaderAccept?: 'application/json';
      context?: HttpContext;
      transferCache?: boolean;
    },
  ): Observable<ApiResponseVoid>;
  public deleteDocumentPrice(
    id: number,
    observe?: 'response',
    reportProgress?: boolean,
    options?: {
      httpHeaderAccept?: 'application/json';
      context?: HttpContext;
      transferCache?: boolean;
    },
  ): Observable<HttpResponse<ApiResponseVoid>>;
  public deleteDocumentPrice(
    id: number,
    observe?: 'events',
    reportProgress?: boolean,
    options?: {
      httpHeaderAccept?: 'application/json';
      context?: HttpContext;
      transferCache?: boolean;
    },
  ): Observable<HttpEvent<ApiResponseVoid>>;
  public deleteDocumentPrice(
    id: number,
    observe: any = 'body',
    reportProgress: boolean = false,
    options?: {
      httpHeaderAccept?: 'application/json';
      context?: HttpContext;
      transferCache?: boolean;
    },
  ): Observable<any> {
    if (id === null || id === undefined) {
      throw new Error(
        'Required parameter id was null or undefined when calling deleteDocumentPrice.',
      );
    }

    let localVarHeaders = this.defaultHeaders;

    // authentication (bearer-jwt) required
    localVarHeaders = this.configuration.addCredentialToHeaders(
      'bearer-jwt',
      'Authorization',
      localVarHeaders,
      'Bearer ',
    );

    const localVarHttpHeaderAcceptSelected: string | undefined =
      options?.httpHeaderAccept ??
      this.configuration.selectHeaderAccept(['application/json']);
    if (localVarHttpHeaderAcceptSelected !== undefined) {
      localVarHeaders = localVarHeaders.set(
        'Accept',
        localVarHttpHeaderAcceptSelected,
      );
    }

    const localVarHttpContext: HttpContext =
      options?.context ?? new HttpContext();

    const localVarTransferCache: boolean = options?.transferCache ?? true;

    let responseType_: 'text' | 'json' | 'blob' = 'json';
    if (localVarHttpHeaderAcceptSelected) {
      if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
        responseType_ = 'text';
      } else if (
        this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)
      ) {
        responseType_ = 'json';
      } else {
        responseType_ = 'blob';
      }
    }

    let localVarPath = `/document-price/${this.configuration.encodeParam({ name: 'id', value: id, in: 'path', style: 'simple', explode: false, dataType: 'number', dataFormat: 'int64' })}`;
    const { basePath, withCredentials } = this.configuration;
    return this.httpClient.request<ApiResponseVoid>(
      'delete',
      `${basePath}${localVarPath}`,
      {
        context: localVarHttpContext,
        responseType: <any>responseType_,
        ...(withCredentials ? { withCredentials } : {}),
        headers: localVarHeaders,
        observe: observe,
        transferCache: localVarTransferCache,
        reportProgress: reportProgress,
      },
    );
  }

  /**
   * Get document prices
   * Retrieves a single document price by ID if provided, or a paginated list of document prices filtered by product code ID or document type ID if provided, or all document prices if no parameters are provided.
   * @param id Document price ID
   * @param productCodeId Product code ID
   * @param documentTypeId Document type ID
   * @param page Page number
   * @param size Page size
   * @param sort Sort field and direction (e.g., id,desc)
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public getDocumentPrices(
    id?: number,
    productCodeId?: number,
    documentTypeId?: number,
    page?: number,
    size?: number,
    sort?: string,
    observe?: 'body',
    reportProgress?: boolean,
    options?: {
      httpHeaderAccept?: 'application/json';
      context?: HttpContext;
      transferCache?: boolean;
    },
  ): Observable<ApiResponsePageDocumentPriceResponseDTO>;
  public getDocumentPrices(
    id?: number,
    productCodeId?: number,
    documentTypeId?: number,
    page?: number,
    size?: number,
    sort?: string,
    observe?: 'response',
    reportProgress?: boolean,
    options?: {
      httpHeaderAccept?: 'application/json';
      context?: HttpContext;
      transferCache?: boolean;
    },
  ): Observable<HttpResponse<ApiResponsePageDocumentPriceResponseDTO>>;
  public getDocumentPrices(
    id?: number,
    productCodeId?: number,
    documentTypeId?: number,
    page?: number,
    size?: number,
    sort?: string,
    observe?: 'events',
    reportProgress?: boolean,
    options?: {
      httpHeaderAccept?: 'application/json';
      context?: HttpContext;
      transferCache?: boolean;
    },
  ): Observable<HttpEvent<ApiResponsePageDocumentPriceResponseDTO>>;
  public getDocumentPrices(
    id?: number,
    productCodeId?: number,
    documentTypeId?: number,
    page?: number,
    size?: number,
    sort?: string,
    observe: any = 'body',
    reportProgress: boolean = false,
    options?: {
      httpHeaderAccept?: 'application/json';
      context?: HttpContext;
      transferCache?: boolean;
    },
  ): Observable<any> {
    let localVarQueryParameters = new HttpParams({ encoder: this.encoder });
    localVarQueryParameters = this.addToHttpParams(
      localVarQueryParameters,
      <any>id,
      'id',
    );
    localVarQueryParameters = this.addToHttpParams(
      localVarQueryParameters,
      <any>productCodeId,
      'productCodeId',
    );
    localVarQueryParameters = this.addToHttpParams(
      localVarQueryParameters,
      <any>documentTypeId,
      'documentTypeId',
    );
    localVarQueryParameters = this.addToHttpParams(
      localVarQueryParameters,
      <any>page,
      'page',
    );
    localVarQueryParameters = this.addToHttpParams(
      localVarQueryParameters,
      <any>size,
      'size',
    );
    localVarQueryParameters = this.addToHttpParams(
      localVarQueryParameters,
      <any>sort,
      'sort',
    );

    let localVarHeaders = this.defaultHeaders;

    // authentication (bearer-jwt) required
    localVarHeaders = this.configuration.addCredentialToHeaders(
      'bearer-jwt',
      'Authorization',
      localVarHeaders,
      'Bearer ',
    );

    const localVarHttpHeaderAcceptSelected: string | undefined =
      options?.httpHeaderAccept ??
      this.configuration.selectHeaderAccept(['application/json']);
    if (localVarHttpHeaderAcceptSelected !== undefined) {
      localVarHeaders = localVarHeaders.set(
        'Accept',
        localVarHttpHeaderAcceptSelected,
      );
    }

    const localVarHttpContext: HttpContext =
      options?.context ?? new HttpContext();

    const localVarTransferCache: boolean = options?.transferCache ?? true;

    let responseType_: 'text' | 'json' | 'blob' = 'json';
    if (localVarHttpHeaderAcceptSelected) {
      if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
        responseType_ = 'text';
      } else if (
        this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)
      ) {
        responseType_ = 'json';
      } else {
        responseType_ = 'blob';
      }
    }

    let localVarPath = `/document-price`;
    const { basePath, withCredentials } = this.configuration;
    return this.httpClient.request<ApiResponsePageDocumentPriceResponseDTO>(
      'get',
      `${basePath}${localVarPath}`,
      {
        context: localVarHttpContext,
        params: localVarQueryParameters,
        responseType: <any>responseType_,
        ...(withCredentials ? { withCredentials } : {}),
        headers: localVarHeaders,
        observe: observe,
        transferCache: localVarTransferCache,
        reportProgress: reportProgress,
      },
    );
  }

  /**
   * Update a document price
   * Updates a document price with the provided details. Required fields: productCodeId, basePrice, effectiveDate, gst. Optional fields: documentTypeId, effectiveBasePrice, expiryDate. Updates will cascade to corresponding CompanySpecialPrice records.
   * @param id Document price ID
   * @param documentPriceRequestDTO
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public updateDocumentPrice(
    id: number,
    documentPriceRequestDTO: DocumentPriceRequestDTO,
    observe?: 'body',
    reportProgress?: boolean,
    options?: {
      httpHeaderAccept?: 'application/json';
      context?: HttpContext;
      transferCache?: boolean;
    },
  ): Observable<ApiResponseDocumentPriceResponseDTO>;
  public updateDocumentPrice(
    id: number,
    documentPriceRequestDTO: DocumentPriceRequestDTO,
    observe?: 'response',
    reportProgress?: boolean,
    options?: {
      httpHeaderAccept?: 'application/json';
      context?: HttpContext;
      transferCache?: boolean;
    },
  ): Observable<HttpResponse<ApiResponseDocumentPriceResponseDTO>>;
  public updateDocumentPrice(
    id: number,
    documentPriceRequestDTO: DocumentPriceRequestDTO,
    observe?: 'events',
    reportProgress?: boolean,
    options?: {
      httpHeaderAccept?: 'application/json';
      context?: HttpContext;
      transferCache?: boolean;
    },
  ): Observable<HttpEvent<ApiResponseDocumentPriceResponseDTO>>;
  public updateDocumentPrice(
    id: number,
    documentPriceRequestDTO: DocumentPriceRequestDTO,
    observe: any = 'body',
    reportProgress: boolean = false,
    options?: {
      httpHeaderAccept?: 'application/json';
      context?: HttpContext;
      transferCache?: boolean;
    },
  ): Observable<any> {
    if (id === null || id === undefined) {
      throw new Error(
        'Required parameter id was null or undefined when calling updateDocumentPrice.',
      );
    }
    if (
      documentPriceRequestDTO === null ||
      documentPriceRequestDTO === undefined
    ) {
      throw new Error(
        'Required parameter documentPriceRequestDTO was null or undefined when calling updateDocumentPrice.',
      );
    }

    let localVarHeaders = this.defaultHeaders;

    // authentication (bearer-jwt) required
    localVarHeaders = this.configuration.addCredentialToHeaders(
      'bearer-jwt',
      'Authorization',
      localVarHeaders,
      'Bearer ',
    );

    const localVarHttpHeaderAcceptSelected: string | undefined =
      options?.httpHeaderAccept ??
      this.configuration.selectHeaderAccept(['application/json']);
    if (localVarHttpHeaderAcceptSelected !== undefined) {
      localVarHeaders = localVarHeaders.set(
        'Accept',
        localVarHttpHeaderAcceptSelected,
      );
    }

    const localVarHttpContext: HttpContext =
      options?.context ?? new HttpContext();

    const localVarTransferCache: boolean = options?.transferCache ?? true;

    // to determine the Content-Type header
    const consumes: string[] = ['application/json'];
    const httpContentTypeSelected: string | undefined =
      this.configuration.selectHeaderContentType(consumes);
    if (httpContentTypeSelected !== undefined) {
      localVarHeaders = localVarHeaders.set(
        'Content-Type',
        httpContentTypeSelected,
      );
    }

    let responseType_: 'text' | 'json' | 'blob' = 'json';
    if (localVarHttpHeaderAcceptSelected) {
      if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
        responseType_ = 'text';
      } else if (
        this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)
      ) {
        responseType_ = 'json';
      } else {
        responseType_ = 'blob';
      }
    }

    let localVarPath = `/document-price/${this.configuration.encodeParam({ name: 'id', value: id, in: 'path', style: 'simple', explode: false, dataType: 'number', dataFormat: 'int64' })}`;
    const { basePath, withCredentials } = this.configuration;
    return this.httpClient.request<ApiResponseDocumentPriceResponseDTO>(
      'put',
      `${basePath}${localVarPath}`,
      {
        context: localVarHttpContext,
        body: documentPriceRequestDTO,
        responseType: <any>responseType_,
        ...(withCredentials ? { withCredentials } : {}),
        headers: localVarHeaders,
        observe: observe,
        transferCache: localVarTransferCache,
        reportProgress: reportProgress,
      },
    );
  }
}
