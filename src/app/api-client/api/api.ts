export * from './companyController.service';
import { CompanyControllerService } from './companyController.service';
export * from './companySpecialPriceController.service';
import { CompanySpecialPriceControllerService } from './companySpecialPriceController.service';
export * from './documentController.service';
import { DocumentControllerService } from './documentController.service';
export * from './documentPriceController.service';
import { DocumentPriceControllerService } from './documentPriceController.service';
export * from './folderController.service';
import { FolderControllerService } from './folderController.service';
export * from './lookUpController.service';
import { LookUpControllerService } from './lookUpController.service';
export * from './orderController.service';
import { OrderControllerService } from './orderController.service';
export * from './permissionController.service';
import { PermissionControllerService } from './permissionController.service';
export * from './roleController.service';
import { RoleControllerService } from './roleController.service';
export * from './s3Controller.service';
import { S3ControllerService } from './s3Controller.service';
export * from './servApiController.service';
import { ServApiControllerService } from './servApiController.service';
export * from './transactionController.service';
import { TransactionControllerService } from './transactionController.service';
export * from './userController.service';
import { UserControllerService } from './userController.service';
export * from './walletController.service';
import { WalletControllerService } from './walletController.service';
export const APIS = [
  CompanyControllerService,
  CompanySpecialPriceControllerService,
  DocumentControllerService,
  DocumentPriceControllerService,
  FolderControllerService,
  LookUpControllerService,
  OrderControllerService,
  PermissionControllerService,
  RoleControllerService,
  S3ControllerService,
  ServApiControllerService,
  TransactionControllerService,
  UserControllerService,
  WalletControllerService,
];
