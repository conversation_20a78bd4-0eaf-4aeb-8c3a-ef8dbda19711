// Color Variables
.text-dark-blue {
  color: #00002e;
}

.text-dark-gray {
  color: #565656;
}

// Main Container
.page-container {
  width: 100%;
  padding: 0;
}

.top-section {
  width: 100%;
}

.map-container {
  width: 100%;
}

.details-box {
  padding: 16px;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

// Property Sections
.property-section {
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }
}

// Headers
.section-header,
.sub-header {
  font-size: 15px;
  font-weight: 600;
  color: #00002e;
  margin: 0 0 8px 0;
  line-height: 1.2;
}

// Primary Address
.address-row {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.address-text {
  font-size: 14px;
  font-weight: 400;
  color: #565656;
  margin: 0;
  line-height: 1.2;
}

// Map Section
.map-section {
  margin: 16px 0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

// Property Details Grid
.details-grid {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  gap: 4px;
  padding: 2px 0;
}

.detail-label {
  font-size: 14px;
  font-weight: 600;
  color: #00002e;
  min-width: 120px;
  flex-shrink: 0;
  line-height: 1.2;
}

.detail-value {
  font-size: 14px;
  font-weight: 400;
  color: #333333;
  line-height: 1.2;
  flex: 1;
}
.secondary-address {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.address-details {
  font-size: 15px;
  font-weight: 500;
  color: #00002e;
  margin: 0 0 8px 0;
  line-height: 1.2;
}

.address-component {
  font-size: 13px;
  font-weight: 400;
  color: #565656;
  line-height: 1.2;

  &:not(:last-child):after {
    content: ',';
    margin-right: 4px;
  }

  &:empty {
    display: none;
  }
}

// Responsive Design
@media (max-width: 768px) {
  .details-box {
    padding: 12px;
  }

  .property-section {
    margin-bottom: 14px;
  }

  .detail-item {
    flex-direction: column;
    gap: 2px;
  }

  .detail-label {
    min-width: auto;
    margin-bottom: 1px;
  }
}

* {
  box-sizing: border-box;
}

p,
h4,
h5 {
  margin: 0;
}

.row {
  margin: 0;

  [class*='col-'] {
    padding: 0;
  }
}
