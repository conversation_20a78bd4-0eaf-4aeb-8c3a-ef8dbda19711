<div class="page-container">
  <!-- Top Section -->
  <div class="top-section">
    <div class="map-container">
      <div class="details-box">
        @if (selectedProperty) {
          <!-- Primary Address Section -->
          <div class="property-section">
            <div class="address-row">
              <h4 class="section-header">Primary Address</h4>
              <p class="address-text">
                {{ selectedProperty.primaryAddress?.eziAddress }}
              </p>
            </div>
          </div>

          <!-- Map Component -->
          <div class="map-section">
            <app-map-view
              [searchText]="selectedProperty.primaryAddress?.eziAddress ?? ''"
              [height]="400"
            >
            </app-map-view>
          </div>

          <!-- Property Details Section -->
          <div class="property-section">
            <h4 class="section-header">Property Details</h4>

            <div class="details-grid">
              <div class="detail-item">
                <span class="detail-label">Property PFI:</span>
                <span class="detail-value">{{
                  selectedProperty.propertyPfi
                }}</span>
              </div>

              <div class="detail-item">
                <span class="detail-label">Status:</span>
                <span class="detail-value">{{
                  selectedProperty.propertyStatus
                }}</span>
              </div>

              <div class="detail-item">
                <span class="detail-label">Title Type:</span>
                <span class="detail-value">{{
                  selectedProperty.titles?.[0]?.titleType
                }}</span>
              </div>

              <div class="detail-item">
                <span class="detail-label">Parcel:</span>
                <span class="detail-value">{{
                  selectedProperty.landParcels?.[0]?.spi
                }}</span>
              </div>
            </div>
          </div>

          <!-- Secondary Address Section -->
          <div class="property-section">
            <h5 class="sub-header">Secondary Address</h5>

            <div class="secondary-address">
              <p class="address-text">
                {{ selectedProperty.aliasAddresses?.[0]?.eziAddress }}
              </p>

              <div class="address-details">
                <span
                  class="address-component"
                  *ngIf="
                    selectedProperty.aliasAddresses?.[0]?.addressDetails
                      ?.buildingName
                  "
                >
                  {{
                    selectedProperty.aliasAddresses?.[0]?.addressDetails
                      ?.buildingName
                  }}
                </span>

                <span
                  class="address-component"
                  *ngIf="
                    selectedProperty.aliasAddresses?.[0]?.addressDetails
                      ?.unitType ||
                    selectedProperty.aliasAddresses?.[0]?.addressDetails
                      ?.unitNumber
                  "
                >
                  {{
                    selectedProperty.aliasAddresses?.[0]?.addressDetails
                      ?.unitType
                  }}
                  {{
                    selectedProperty.aliasAddresses?.[0]?.addressDetails
                      ?.unitNumber
                  }}
                </span>

                <span class="address-component">
                  {{
                    selectedProperty.aliasAddresses?.[0]?.addressDetails
                      ?.streetNumber
                  }}
                  {{
                    selectedProperty.aliasAddresses?.[0]?.addressDetails
                      ?.streetName
                  }}
                  {{
                    selectedProperty.aliasAddresses?.[0]?.addressDetails
                      ?.streetType
                  }}
                  {{
                    selectedProperty.aliasAddresses?.[0]?.addressDetails
                      ?.streetSuffix
                  }}
                </span>

                <span class="address-component">
                  {{
                    selectedProperty.aliasAddresses?.[0]?.addressDetails
                      ?.suburbTownLocality
                  }}
                  {{
                    selectedProperty.aliasAddresses?.[0]?.addressDetails
                      ?.postcode
                  }}
                </span>
              </div>
            </div>
          </div>
        }
      </div>
    </div>
  </div>
</div>
