import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PropertyDetailsDrawerComponent } from './property-details-drawer.component';
import { IPropertySearch } from '../../../core/interface/property-search.interface';
import { PropertyDTO, LandParcelDTO, TitleDTO } from '../../../api-client';

@Component({
  selector: 'app-map-view',
  template: '<div class="mock-map">Map Placeholder</div>',
  standalone: true,
})
class MockMapViewComponent {
  searchText = '';
  height = 400;
}

const baseMockProperty: IPropertySearch = {
  primaryAddress: { eziAddress: '123 Main St, Springfield, 62701' },
  propertyPfi: 123,
  propertyStatus: PropertyDTO.PropertyStatusEnum.Active,
  isCouncilPropertyRegistered: false,
  isMultiAssess: false,
  propertyCreatedDate: '2023-01-01',
  aliasAddresses: [],
  landParcels: [
    {
      spi: 'Parcel123',
      parcelType: LandParcelDTO.ParcelTypeEnum.Lot,
      lotType: LandParcelDTO.LotTypeEnum.Unrestricted,
      parcelStatus: LandParcelDTO.ParcelStatusEnum.Active,
    },
  ],
  titles: [
    {
      titleType: TitleDTO.TitleTypeEnum.Freehold,
      titleStatus: TitleDTO.TitleStatusEnum.Active,
    },
  ],
};

describe('PropertyDetailsDrawerComponent', () => {
  let component: PropertyDetailsDrawerComponent;
  let fixture: ComponentFixture<PropertyDetailsDrawerComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        CommonModule,
        PropertyDetailsDrawerComponent,
        MockMapViewComponent,
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(PropertyDetailsDrawerComponent);
    component = fixture.componentInstance;
  });

  it('should create the component', () => {
    fixture.detectChanges();
    expect(component).toBeTruthy();
  });

  it('should not show any property data if selectedProperty is undefined', () => {
    component.selectedProperty = undefined;
    fixture.detectChanges();
    const compiled = fixture.nativeElement as HTMLElement;
    expect(compiled.textContent).not.toContain('Primary Address');
  });

  it('should display primary address when selectedProperty is set', () => {
    component.selectedProperty = baseMockProperty;
    fixture.detectChanges();
    const compiled = fixture.nativeElement as HTMLElement;
    expect(compiled.textContent).toContain('123 Main St, Springfield, 62701');
  });
  it('should display property PFI, status, title, and parcel', () => {
    component.selectedProperty = baseMockProperty;
    fixture.detectChanges();
    const compiled = fixture.nativeElement as HTMLElement;

    expect(compiled.textContent).toContain('Property PFI');
    expect(compiled.textContent).toContain('123');
    expect(compiled.textContent).toContain('ACTIVE');
    expect(compiled.textContent).toContain('FREEHOLD');
    expect(compiled.textContent).toContain('Parcel123');
  });
});
