import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IPropertySearch } from '../../../core/interface/property-search.interface';
import { MapViewComponent } from '../../shared/map-search/map-view.component';

@Component({
  selector: 'app-property-details-drawer',
  imports: [MapViewComponent, CommonModule],
  templateUrl: './property-details-drawer.component.html',
  styleUrl: './property-details-drawer.component.scss',
})
export class PropertyDetailsDrawerComponent {
  @Input() selectedProperty?: IPropertySearch;
}
