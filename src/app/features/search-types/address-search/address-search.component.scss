/* Add border radius and box shadow to nz-select */
.search-input-container nz-select {
  ::ng-deep .ant-select {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  ::ng-deep .ant-select-selector {
    border-radius: 8px !important;
    height: calc(100% + 10px) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #ffffff;
  }
}

/* Your existing styles */
.address-search {
  font-size: 1.8rem;
  font-weight: 600;
  color: #2c3e50;
  margin-left: 8px;
}

.search-input-container {
  display: flex;
  border-radius: 20px;
  align-items: center;
  justify-content: space-between;
  gap: 15px;
}

.sample-input-text {
  font-size: 0.9rem;
  color: #888;
  margin-top: 0.5rem;
}

.button-container {
  margin: 2rem 0;
  text-align: right;
}
.map-container {
  width: 100%;
  height: 100%;
  padding: 1.5rem;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  background-color: #ffffff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);

  .map-content {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    align-items: flex-start;

    .map-image {
      flex: 1;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 12px 0 0 12px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }
    }

    .property-details {
      flex: 1;
      min-width: 300px;
      background: #fdfdfd;
      padding: 1.5rem;
      border-radius: 12px;
      border: 1px solid #eaeaea;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

      h2 {
        font-size: 1.6rem;
        font-weight: 600;
        color: #34495e;
        margin-bottom: 1rem;
      }

      h3 {
        font-size: 1.3rem;
        font-weight: 500;
        color: #7f8c8d;
        margin-top: 1.5rem;
        margin-bottom: 0.75rem;
      }

      p {
        font-size: 1rem;
        color: #555;
        line-height: 1.5;
        margin: 0.4rem 0;
        word-wrap: break-word;

        strong {
          color: #2c3e50;
        }
      }
    }
  }
}

nz-splitter {
  height: 100%;
  min-height: 500px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border-radius: 12px;
  overflow: hidden;
}

.map-box {
  height: 100%;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 12px 0 0 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

.details-box {
  height: 100%;
  padding: 1.5rem;
  background-color: #fdfdfd;
  border-left: 1px solid #eee;

  h2 {
    font-size: 1.6rem;
    color: #2c3e50;
    margin-bottom: 1rem;
  }

  h3 {
    font-size: 1.3rem;
    color: #7f8c8d;
    margin-top: 1.5rem;
  }

  p {
    font-size: 1rem;
    color: #555;
    line-height: 1.6;
    margin: 0.3rem 0;

    strong {
      color: #34495e;
    }
  }
}