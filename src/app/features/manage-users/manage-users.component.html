<div>
  <div class="d-flex justify-content-end mb-3">
    <button nz-button nzType="primary" (click)="addUserFormVisible()">
      <i nz-icon nzType="plus" nzTheme="outline"></i> Add User
    </button>
  </div>

  <app-data-grid
    class="mt-3"
    *ngIf="manageActiveUsersTableData.length > 0"
    [tableColumns]="manageUserTableColumns"
    [tableData]="manageActiveUsersTableData"
    [loading]="isLoading"
    [defaultSortParam]="'firstName'"
    [showPagination]="false"
    (tableDataClick)="onTableDataClick($event)"
  ></app-data-grid>

  <!-- Accordion for Deleted Users -->
  <div class="deleted-users-accordion">
    <div
      class="accordion-header d-flex align-items-center inactive-accordian"
      (click)="toggleDeletedUsersAccordion()"
      role="button"
      aria-label="Toggle deleted users"
    >
      <span class="inactive-users-text">View Inactive Users</span>
      <i
        class="fa-solid chevron"
        [ngClass]="
          isManageInactiveUsersAccordionOpen
            ? 'fa-chevron-right'
            : 'fa-chevron-up'
        "
      ></i>
    </div>
    <div *ngIf="isManageInactiveUsersAccordionOpen" class="accordion-content">
      <div
        *ngIf="manageInactiveUsersTableData.length === 0"
        class="text-center p-3"
      >
        No deleted users found.
      </div>
      <app-data-grid
        *ngIf="manageInactiveUsersTableData.length > 0"
        class="deleted-user-table-datagrid"
        [tableColumns]="manageUserTableColumns"
        [tableData]="manageInactiveUsersTableData"
        [loading]="isLoading"
        [defaultSortParam]="'firstName'"
        [showPagination]="false"
        (tableDataClick)="onTableDataClick($event)"
        [ngClass]="{ 'deleted-users-table': true }"
      ></app-data-grid>
    </div>
  </div>

  <!-- Add/Edit User Drawer -->
  <app-add-user
    [isVisible]="isAddUserFormVisible"
    [companyId]="companyId"
    [company]="companyInfo"
    [existingUsers]="manageActiveUsersTableData"
    [userToEdit]="userToEdit"
    (drawerClosed)="onDrawerClosed()"
    (userAdded)="onUserAdded($event)"
    (userUpdated)="onUserUpdated($event)"
    [showBreadcrumbs]="true"
  ></app-add-user>
</div>
