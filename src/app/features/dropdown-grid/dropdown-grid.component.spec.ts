import { ComponentFixture, TestBed } from '@angular/core/testing';
import { of } from 'rxjs';
import { DropdownGridComponent } from './dropdown-grid.component';
import { TransactionService } from '../../core/services/transaction.service';
import { EnumTransactionTypes } from '../../core/enumerations/transaction-types';
import { TransactionDTO } from '../../api-client';

describe('DropdownGridComponent', () => {
  let component: DropdownGridComponent;
  let fixture: ComponentFixture<DropdownGridComponent>;
  let mockTransactionService: jasmine.SpyObj<TransactionService>;
  let mockTransactions: TransactionDTO[];

  beforeEach(() => {
    mockTransactions = [
      {
        transactionId: 1,
        transactionType: EnumTransactionTypes.RECHARGE,
        transactionDate: '2023-01-01',
        amount: 100,

        referenceId: 'ref1',
        paymentMethod: 'CREDIT_CARD',
      },
      {
        transactionId: 2,
        transactionType: EnumTransactionTypes.PURCHASE,
        transactionDate: '2023-01-02',
        amount: 50,

        referenceId: 'ref2',
        paymentMethod: 'CREDIT_CARD',
      },
    ];
  });

  beforeEach(async () => {
    mockTransactionService = jasmine.createSpyObj('TransactionService', [
      'getAllTransactions',
    ]);
    mockTransactionService.getAllTransactions.and.returnValue(
      of(mockTransactions),
    );

    await TestBed.configureTestingModule({
      imports: [DropdownGridComponent],
      providers: [
        { provide: TransactionService, useValue: mockTransactionService },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(DropdownGridComponent);
    component = fixture.componentInstance;
    fixture.detectChanges(); // triggers ngOnInit
  });

  it('should create component', () => {
    expect(component).toBeTruthy();
  });

  it('should toggle collapse state', () => {
    expect(component.isExpanded).toBeFalse();
    component.toggleCollapse();
    expect(component.isExpanded).toBeTrue();
    component.toggleCollapse();
    expect(component.isExpanded).toBeFalse();
  });

  it('should update isExpanded on panel change', () => {
    component.onCollapseChange([0]);
    expect(component.isExpanded).toBeTrue();

    component.onCollapseChange([]);
    expect(component.isExpanded).toBeFalse();
  });
});
