.tab-container {
  position: relative;
  width: 100%;
  margin: 0;
  background: var(--primary-dropdown-bg-color, #f1f9ff);
  box-shadow: none;
  border: 1px solid var(--primary-dropdown-bg-color, #f1f9ff);
  margin-top: 35px;
  padding-top: 0;
}

.arrow-container {
  position: absolute;
  top: -1.5rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.arrow-top {
  background-color: var(--primary-button-color, #1890ff);
  border: none;
  cursor: pointer;
  font-size: 16px;
  height: 1.5rem; 
  width: 4rem;
  display: flex;
  align-items: center;
  justify-content: center;
  clip-path: polygon(20% 0, 80% 0, 100% 100%, 0% 100%);
  z-index: 1000;
  transition: all 0.3s ease;
}

.arrow-top:focus {
  outline: none;
}

.arrow-top i {
  color: #ffffff;
  font-size: 16px;
  transform: rotate(-90deg);
}

.header-section {
  background-color: var(--primary-dropdown-bg-color, #f1f9ff);
  padding: 12px 20px;
  border-bottom: none;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  user-select: none;
  border-radius: 8px 8px 0 0;
}

.header-title {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  color: #000000;
  text-align: center;
  flex-grow: 1;
  background-color: var(--primary-dropdown-bg-color, #f1f9ff);
}

.collapse-icon {
  background: var(--primary-dropdown-bg-color, #f1f9ff);
  border: none;
  font-size: 16px;
  color: #000000;
  cursor: pointer;
  padding: 0;
  margin: 0;
}

.collapse-icon i.fa {
  transition: transform 0.3s ease;
  color: #000000;
}

.content-wrapper {
  background: var(--primary-dropdown-bg-color, #f1f9ff);
  border-radius: 0;
  overflow: hidden;
  transition: height, max-height 0.5s ease;
  overflow-x: auto;
}

.content-wrapper.expanded {
  max-height: 100vh;
  height: 100%;
}

.content-wrapper.collapsed {
  max-height: 0;
  height: 0;
}

.transaction-table {
  background-color: var(--primary-dropdown-bg-color, #f1f9ff) !important;
}

.transaction-table .ant-table-thead > tr > th {
  background-color: var(--primary-dropdown-bg-color, #f1f9ff);
  text-align: center;
  font-weight: 600;
  color: #000000;
  border-bottom: 1px solid var(--primary-dropdown-bg-color, #f1f9ff);
  padding: 12px 16px;
  font-size: 13px;
}

.transaction-table .ant-table-tbody > tr > td {
  text-align: center;
  padding: 12px 16px;
  border-bottom: 1px solid var(--primary-dropdown-bg-color, #f1f9ff);
  color: #000000;
  font-size: 13px;
  background-color: var(--primary-dropdown-bg-color, #f1f9ff);
}

.transaction-table .ant-table-tbody > tr:hover > td {
  background-color: var(--primary-dropdown-bg-color, #f1f9ff);
  color: #000000;
}

.transaction-table .ant-table-container {
  border-radius: 0;
  border: none;
  background-color: var(--primary-dropdown-bg-color, #f1f9ff);
}

.transaction-table .ant-table {
  border-radius: 0;
  border: none;
  background-color: var(--primary-dropdown-bg-color, #f1f9ff);
}

.transaction-table .ant-table-thead > tr:first-child > th:first-child {
  border-radius: 0;
  background-color: var(--primary-dropdown-bg-color, #f1f9ff);
}

.transaction-table .ant-table-thead > tr:first-child > th:last-child {
  border-radius: 0;
  background-color: var(--primary-dropdown-bg-color, #f1f9ff);
}

.transaction-table .ant-table-thead > tr > th:first-child {
  border-left: none;
  background-color: var(--primary-dropdown-bg-color, #f1f9ff);
}

.transaction-table .ant-table-thead > tr > th:last-child {
  border-right: none;
  background-color: var(--primary-dropdown-bg-color, #f1f9ff);
}

.transaction-table .ant-table-tbody > tr > td:first-child {
  border-left: none;
  background-color: var(--primary-dropdown-bg-color, #f1f9ff);
}

.transaction-table .ant-table-tbody > tr > td:last-child {
  border-right: none;
  background-color: var(--primary-dropdown-bg-color, #f1f9ff);
}

.transaction-table .ant-table-content {
  border: none;
  background-color: var(--primary-dropdown-bg-color, #f1f9ff);
}

.transaction-table .ant-table-container::before {
  display: none;
}

.transaction-table .ant-table-container::after {
  display: none;
}

.transaction-table .ant-table-tbody {
  background-color: var(--primary-dropdown-bg-color, #f1f9ff);
}

.transaction-table .ant-table-tbody > tr {
  background-color: var(--primary-dropdown-bg-color, #f1f9ff);
}

.transaction-table .ant-table-wrapper {
  background-color: var(--primary-dropdown-bg-color, #f1f9ff);
}
