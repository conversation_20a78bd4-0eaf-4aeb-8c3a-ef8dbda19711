<!-- HTML Template -->
<div class="tab-container">
  <!-- Arrow at the top -->
  @if (!hideArrow) {
    <div class="arrow-container">
      <div class="arrow-top" (click)="toggleCollapse()">
        <i
          class="fa"
          [ngClass]="{
            'fa-angle-left': isExpanded,
            'fa-angle-right': !isExpanded
          }"
        ></i>
      </div>
    </div>
  }
  <!-- Header section with toggle icon inside -->
  <div
    class="header-section"
    (click)="toggleCollapse()"
    tabindex="0"
    role="button"
    aria-label="Toggle Recharge History"
    (keydown.enter)="toggleCollapse()"
    (keydown.space)="toggleCollapse()"
  >
    @if (!isExpanded) {
      <h3 class="header-title">View Recharge History</h3>
    }
    <!-- Collapsible Content: Transaction Table -->
  </div>
  <div
    class="content-wrapper"
    [class.expanded]="isExpanded"
    [class.collapsed]="!isExpanded"
  >
    <nz-table
      [nzData]="transactionData"
      [nzShowPagination]="false"
      [nzFrontPagination]="false"
      class="transaction-table"
      [nzScroll]="{ y: '25rem' }"
    >
      <thead>
        <tr>
          <th>Transaction ID</th>
          <th>Date & Time</th>
          <th>Amount</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let txn of transactionData">
          <td>{{ txn.transactionId }}</td>
          <td>{{  txn.transactionDate | date: 'MMM d, y ' }}
            {{ txn.transactionDate | date: 'h:mm a' }}
          </td>
          <td>${{ txn.amount | number: '1.2-2' }}</td>
        </tr>
      </tbody>
    </nz-table>
  </div>
</div>
