import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { provideHttpClient } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { of } from 'rxjs';

import { CompanyUserOnboardFormComponent } from './company-user-onboard-form.component';
import { NotificationService } from '../../../core/services/notification.service';
import { CompanyState } from 'src/app/core/components/company-state';
import { SharedLookup } from 'src/app/core/components/shared-lookup';
import { UserCreation } from 'src/app/core/components/user-creation';

describe('CompanyUserOnboardFormComponent', () => {
  let component: CompanyUserOnboardFormComponent;
  let fixture: ComponentFixture<CompanyUserOnboardFormComponent>;
  let mockNotificationService: jasmine.SpyObj<NotificationService>;
  let mockSharedLookupService: jasmine.SpyObj<SharedLookup>;
  let mockCompanyStateService: jasmine.SpyObj<CompanyState>;
  let mockUserCreationService: jasmine.SpyObj<UserCreation>;

  beforeEach(async () => {
    mockNotificationService = jasmine.createSpyObj('NotificationService', [
      'error',
      'success',
      'warning',
    ]);
    mockSharedLookupService = jasmine.createSpyObj('SharedLookupService', [
      'fetchRoles',
    ]);
    mockCompanyStateService = jasmine.createSpyObj('CompanyStateService', [
      'getCurrentOnboardingState',
      'updateUserDetails',
      'fileSelected$',
      'triggerFileInput',
      'fileUploadEvent',
    ]);
    mockUserCreationService = jasmine.createSpyObj('UserCreationService', [
      'createUserWithProfilePicture',
      'handleFileSelection',
    ]);

    mockSharedLookupService.fetchRoles.and.returnValue(of([]));
    mockCompanyStateService.getCurrentOnboardingState.and.returnValue({
      companyDetails: null,
      billingDetails: null,
      userDetails: null,
      currentStep: 0,
    });
    mockCompanyStateService.fileSelected$ = of({ id: 1, fileData: null });

    await TestBed.configureTestingModule({
      imports: [
        CompanyUserOnboardFormComponent,
        ReactiveFormsModule,
        NoopAnimationsModule,
      ],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        { provide: NotificationService, useValue: mockNotificationService },
        { provide: SharedLookup, useValue: mockSharedLookupService },
        { provide: CompanyState, useValue: mockCompanyStateService },
        { provide: UserCreation, useValue: mockUserCreationService },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(CompanyUserOnboardFormComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize form with default values', () => {
    expect(component.addUserForm).toBeDefined();
    expect(component.addUserForm.length).toBeGreaterThan(0);
  });

  it('should fetch roles on init', () => {
    component.ngOnInit();
    expect(mockSharedLookupService.fetchRoles).toHaveBeenCalled();
  });

  it('should validate required fields', () => {
    expect(component.addUserForm.length).toBeGreaterThan(0);
  });

  it('should validate email format', () => {
    expect(component.addUserForm.length).toBeGreaterThan(0);
  });

  it('should handle file selection', () => {
    const mockFile = new File([''], 'john_doe_profile.jpg', {
      type: 'image/jpeg',
    });
    const mockEvent = { target: { files: [mockFile] } } as unknown as Event;

    component.onFileSelected(mockEvent);

    expect(mockUserCreationService.handleFileSelection).toHaveBeenCalled();
  });

  it('should trigger file input', () => {
    expect(component.triggerFileInput).toBeDefined();
  });

  it('should check if form has users', () => {
    expect(component.hasUsers()).toBeFalse();

    component.addedUsers = [
      {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        id: 1001,
      },
    ];
    expect(component.hasUsers()).toBeTrue();
  });

  it('should get users count', () => {
    expect(component.getUsersCount()).toBe(0);

    component.addedUsers = [
      {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        id: 1001,
      },
      {
        firstName: 'Jane',
        lastName: 'Smith',
        email: '<EMAIL>',
        id: 1002,
      },
    ];
    expect(component.getUsersCount()).toBe(2);
  });

  it('should add another user', () => {
    component.addAnotherUser();
    expect(component.addAnotherUser).toBeDefined();
  });

  it('should validate add user form', () => {
    expect(component.isAddUserValid()).toBeDefined();
  });

  it('should handle file selection', () => {
    const mockFile = new File([''], 'sarah_wilson_profile.jpg', {
      type: 'image/jpeg',
    });
    const mockEvent = { target: { files: [mockFile] } } as unknown as Event;

    component.onFileSelected(mockEvent, 0);

    expect(mockUserCreationService.handleFileSelection).toHaveBeenCalled();
  });

  it('should trigger file input', () => {
    expect(component.triggerFileInput).toBeDefined();
  });

  it('should get error tip for form controls', () => {
    const firstForm = component.addUserForm.at(0);
    const firstNameControl = firstForm.get('firstName');
    firstNameControl?.markAsTouched();
    firstNameControl?.setValue('');

    const errorTip = component.getErrorTip('firstName');
    expect(errorTip).toContain('FirstName is required');
  });

  it('should handle loading state', () => {
    expect(component.isLoading).toBeFalse();

    component.isLoading = true;
    expect(component.isLoading).toBeTrue();
  });
});
