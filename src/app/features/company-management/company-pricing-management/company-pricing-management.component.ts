import {
  Component,
  Input,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzDrawerModule } from 'ng-zorro-antd/drawer';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzModalService } from 'ng-zorro-antd/modal';

import {
  ApiResponseCompanySpecialPriceResponseDTO,
  ApiResponsePageCompanySpecialPriceResponseDTO,
  CompanySpecialPriceRequestDTO,
} from '../../../api-client';
import { COMMON_STRINGS } from '../../../core/constants/common';
import { DataGridComponent } from '../../data-grid/data-grid.component';
import {
  IPricingFields,
  IPricingViewDetails,
} from '../../../core/interface/pricing-fields';
import { ITableDataClickOutput } from '../../../core/interface/table';
import { NotificationService } from '../../../core/services/notification.service';
import { PricingService } from '../../../core/services/pricing.service';
import {
  DUMMY_PRICING_VIEW_DATA,
  PRICING_MANAGEMENT_COMPANY_LEVEL_TABLE_COLUMNS,
  VIEW_PRICING_DETAILS_COLUMNS,
} from '../../../core/tableColumns/pricing.column';
@Component({
  selector: 'app-company-pricing-management',
  standalone: true,
  imports: [
    CommonModule,
    NzDrawerModule,
    NzButtonModule,
    NzIconModule,
    DataGridComponent,
  ],
  templateUrl: './company-pricing-management.component.html',
  styleUrls: ['./company-pricing-management.component.scss'],
})
export class CompanyPricingManagementComponent implements OnInit {
  @ViewChild('confirmModal') confirmModal!: TemplateRef<unknown>;
  @Input() companyId: number | undefined;
  pricingManagementCompanyLevelTableColumns =
    PRICING_MANAGEMENT_COMPANY_LEVEL_TABLE_COLUMNS;
  pricingManagementTableData: IPricingFields[] = [];
  openPricingView = false;
  viewPricingTableColumns = VIEW_PRICING_DETAILS_COLUMNS;
  viewPricingTableData: IPricingViewDetails[] = DUMMY_PRICING_VIEW_DATA;
  isLoading = false;
  isPriceModalVisible = false;
  selectedRowData: IPricingFields | null = null;
  selectedDocumentId: number | undefined;

  constructor(
    private pricingService: PricingService,
    private modalService: NzModalService,
    private notification: NotificationService,
  ) {}

  ngOnInit() {
    if (this.companyId) {
      this.fetchCompanyPricingData();
    }
  }

  onPricingManagementTableDataClick(
    data: ITableDataClickOutput<IPricingFields>,
  ): void {
    const { actionField, rowData } = data;
    switch (actionField) {
      case 'view':
        this.viewPricingDetails();
        break;
      case 'save':
        this.selectedDocumentId = rowData.id;
        this.openPricingModal(rowData);
        break;
    }
  }

  viewPricingDetails(): void {
    this.openPricingView = true;
  }

  onClosePricingDetails() {
    this.openPricingView = false;
  }

  openPricingModal(rowData: IPricingFields): void {
    this.selectedRowData = rowData;
    this.isPriceModalVisible = true;
    this.modalService.create({
      nzTitle: 'Update Special Price',
      nzContent: this.confirmModal,
      nzFooter: null,
      nzClassName: 'custom-modal',
      nzOnCancel: () => this.handlePriceModalCancel(),
    });
  }

  handlePriceModalSave(): void {
    if (!this.selectedDocumentId) {
      console.error(
        'No document ID selected for update. SelectedDocumentId:',
        this.selectedDocumentId,
      );
      this.notification.error(COMMON_STRINGS.warningMessages.noDocSelected);
      return;
    }

    const specialPrice =
      parseFloat(this.selectedRowData?.specialPrice?.toString() || '0') || 0;
    const updatedRequestDTO: CompanySpecialPriceRequestDTO = {
      specialPrice: specialPrice,
      companyId: this.companyId!,
    };

    this.isLoading = true;
    this.pricingService
      .updateCompanySpecialDocumentPrice(
        this.selectedDocumentId,
        updatedRequestDTO,
      )
      .subscribe({
        next: (response: ApiResponseCompanySpecialPriceResponseDTO) => {
          if (response && response.data) {
            const updatedDoc: IPricingFields = {
              ...response.data,
              ...response.data.documentPrice,
              specialPrice: response.data.specialPrice,
              specialPriceGst: response.data.specialPriceGst,
              productPriceIncGst:
                response.data.specialPrice !== null
                  ? (response.data.specialPrice ?? 0) +
                    (response.data.specialPriceGst ?? 0)
                  : response.data.documentPrice?.effectiveBasePrice !== null
                    ? (response.data.documentPrice?.effectiveBasePrice ?? 0) +
                      (response.data.documentPrice?.effectivePriceGst ?? 0)
                    : (response.data.documentPrice?.basePrice ?? 0) +
                      (response.data.documentPrice?.effectivePriceGst ?? 0),
            };

            const index = this.pricingManagementTableData.findIndex(
              (doc) => doc.id === this.selectedDocumentId,
            );
            if (index !== -1) {
              this.pricingManagementTableData[index] = updatedDoc;
              this.pricingManagementTableData = [
                ...this.pricingManagementTableData,
              ];
            } else {
              console.error(
                'Document with ID',
                this.selectedDocumentId,
                'not found in table data',
              );
            }

            this.notification.success(
              COMMON_STRINGS.successMessages.documentPriceUpdateSuccess.replace(
                '${documentName}',
                updatedDoc.name || '',
              ),
            );
            this.handlePriceModalCancel();
          } else {
            this.notification.error(
              COMMON_STRINGS.errorMessages.failedToUpdateDocumentPrice,
            );
          }
          this.isLoading = false;
        },
        error: (err) => {
          console.error('Update special price error:', err);
          this.notification.error(
            COMMON_STRINGS.errorMessages.failedToUpdateDocumentPrice,
          );
          this.isLoading = false;
        },
      });
  }
  handlePriceModalCancel(): void {
    this.isPriceModalVisible = false;
    this.selectedRowData = null;
    this.modalService.closeAll();
  }

  fetchCompanyPricingData() {
    if (!this.companyId) return;

    this.isLoading = true;
    this.pricingService
      .getAllCompanySpecialDocumentPrices(this.companyId)
      .subscribe({
        next: (response: ApiResponsePageCompanySpecialPriceResponseDTO) => {
          if (response?.data?.content) {
            this.pricingManagementTableData = response.data.content.map(
              (item) => ({
                ...item.documentPrice,
                ...item,
                productPriceIncGst:
                  item.specialPrice !== null
                    ? (item.specialPrice ?? 0) + (item.specialPriceGst ?? 0)
                    : (item.documentPrice?.effectiveBasePrice !== null
                        ? (item.documentPrice?.effectiveBasePrice ?? 0) +
                          (item.documentPrice?.effectivePriceGst ?? 0)
                        : (item.documentPrice?.basePrice ?? 0)) +
                      (item.documentPrice?.effectivePriceGst ?? 0),
              }),
            );
          } else {
            console.warn('No pricing data found in response');
            this.pricingManagementTableData = [];
            this.selectedDocumentId = undefined;
          }
          this.isLoading = false;
        },
        error: (err) => {
          console.error(COMMON_STRINGS.errorMessages.failedToFetchData, err);
          this.pricingManagementTableData = [];
          this.selectedDocumentId = undefined;
          this.isLoading = false;
        },
      });
  }
}
