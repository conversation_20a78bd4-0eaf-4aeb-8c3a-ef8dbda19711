import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CommonModule } from '@angular/common';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzDrawerModule } from 'ng-zorro-antd/drawer';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzModalService } from 'ng-zorro-antd/modal';
import { of } from 'rxjs';
import { CompanyPricingManagementComponent } from './company-pricing-management.component';
import { NotificationService } from '../../../core/services/notification.service';
import { PricingService } from '../../../core/services/pricing.service';
import { DataGridComponent } from '../../data-grid/data-grid.component';
import { IPricingFields } from '../../../core/interface/pricing-fields';
import { ITableDataClickOutput } from '../../../core/interface/table';

describe('CompanyPricingManagementComponent', () => {
  let component: CompanyPricingManagementComponent;
  let fixture: ComponentFixture<CompanyPricingManagementComponent>;
  let pricingService: jasmine.SpyObj<PricingService>;
  let notificationService: jasmine.SpyObj<NotificationService>;
  let modalService: jasmine.SpyObj<NzModalService>;

  beforeEach(async () => {
    pricingService = jasmine.createSpyObj('PricingService', [
      'getAllCompanySpecialDocumentPrices',
      'updateCompanySpecialDocumentPrice',
    ]);
    notificationService = jasmine.createSpyObj('NotificationService', [
      'success',
      'error',
    ]);
    modalService = jasmine.createSpyObj('NzModalService', [
      'create',
      'closeAll',
    ]);

    await TestBed.configureTestingModule({
      imports: [
        CommonModule,
        NzButtonModule,
        NzDrawerModule,
        NzIconModule,
        DataGridComponent, // Import the real DataGridComponent
        CompanyPricingManagementComponent, // Import the standalone component
      ],
      providers: [
        { provide: PricingService, useValue: pricingService },
        { provide: NotificationService, useValue: notificationService },
        { provide: NzModalService, useValue: modalService },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(CompanyPricingManagementComponent);
    component = fixture.componentInstance;
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with companyId and fetch pricing data', () => {
    const mockResponse = {
      data: {
        content: [
          { id: 1, name: 'Doc1', specialPrice: 100, productPriceIncGst: 110 },
        ],
      },
    };
    pricingService.getAllCompanySpecialDocumentPrices.and.returnValue(
      of(mockResponse),
    );
    component.companyId = 123;
    component.ngOnInit();
    expect(
      pricingService.getAllCompanySpecialDocumentPrices,
    ).toHaveBeenCalledWith(123);
    expect(component.pricingManagementTableData.length).toBe(1);
    expect(component.isLoading).toBeFalse();
  });

  it('should open pricing modal when save action is clicked', () => {
    const mockRowData: IPricingFields = {
      id: 1,
      name: 'Doc1',
      specialPrice: 100,
      productPriceIncGst: 110,
    };
    const tableData: ITableDataClickOutput<IPricingFields> = {
      actionField: 'save',
      rowData: mockRowData,
      rowIndex: 0,
    };
    component.onPricingManagementTableDataClick(tableData);
    expect(component.isPriceModalVisible).toBeTrue();
    expect(component.selectedRowData).toBe(mockRowData);
    expect(modalService.create).toHaveBeenCalled();
  });

  it('should open pricing details drawer when view action is clicked', () => {
    const tableData: ITableDataClickOutput<IPricingFields> = {
      actionField: 'view',
      rowData: { id: 1, name: 'Doc1', productPriceIncGst: 0 },
      rowIndex: 0,
    };
    component.onPricingManagementTableDataClick(tableData);
    expect(component.openPricingView).toBeTrue();
  });

  it('should close pricing details drawer', () => {
    component.openPricingView = true;
    component.onClosePricingDetails();
    expect(component.openPricingView).toBeFalse();
  });

  it('should handle price modal cancel', () => {
    component.isPriceModalVisible = true;
    component.selectedRowData = { id: 1, name: 'Doc1', productPriceIncGst: 0 };
    component.handlePriceModalCancel();
    expect(component.isPriceModalVisible).toBeFalse();
    expect(component.selectedRowData).toBeNull();
    expect(modalService.closeAll).toHaveBeenCalled();
  });

  it('should update special price and show success notification', () => {
    const mockRowData: IPricingFields = {
      id: 1,
      name: 'Doc1',
      specialPrice: 150,
      productPriceIncGst: 165,
    };
    component.companyId = 123;
    component.selectedRowData = mockRowData;
    component.selectedDocumentId = 1; // Set the selectedDocumentId
    component.isPriceModalVisible = true;
    component.pricingManagementTableData = [mockRowData]; // Add to table data for update

    const mockResponse = {
      data: {
        id: 1,
        name: 'Doc1',
        specialPrice: 150,
        specialPriceGst: 15,
        productPriceIncGst: 165,
        documentPrice: { basePrice: 100, effectiveBasePrice: 100 },
      },
    };
    pricingService.updateCompanySpecialDocumentPrice.and.returnValue(
      of(mockResponse),
    );

    component.handlePriceModalSave();
    expect(
      pricingService.updateCompanySpecialDocumentPrice,
    ).toHaveBeenCalledWith(1, { specialPrice: 150, companyId: 123 });
    expect(notificationService.success).toHaveBeenCalled();
    expect(component.isLoading).toBeFalse();
    expect(component.isPriceModalVisible).toBeFalse();
  });

  it('should show error notification if no row data selected for price update', () => {
    component.selectedRowData = null;
    component.handlePriceModalSave();
    expect(notificationService.error).toHaveBeenCalled();
    expect(component.isLoading).toBeFalse();
  });
});
