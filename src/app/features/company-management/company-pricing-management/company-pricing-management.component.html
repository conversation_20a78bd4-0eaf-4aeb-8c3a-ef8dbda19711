<div class="company-pricing-management-container">
  <app-data-grid
    [tableColumns]="pricingManagementCompanyLevelTableColumns"
    [tableData]="pricingManagementTableData"
    [loading]="isLoading"
    [showPagination]="false"
    (tableDataClick)="onPricingManagementTableDataClick($event)"
    data-testid="company-pricing-table"
  >
  </app-data-grid>

  <nz-drawer
    [nzClosable]="false"
    [nzVisible]="openPricingView"
    [nzTitle]="drawerTitle"
    [nzExtra]="extra"
    (nzOnClose)="onClosePricingDetails()"
    data-testid="pricing-details-drawer"
  >
    <ng-container *nzDrawerContent>
      <app-data-grid
        [tableColumns]="viewPricingTableColumns"
        [tableData]="viewPricingTableData"
        [loading]="isLoading"
        [showPagination]="false"
        data-testid="view-pricing-table"
      >
      </app-data-grid>
    </ng-container>
  </nz-drawer>

  <ng-template #drawerTitle>
    <h2>View Pricing</h2>
  </ng-template>

  <ng-template #extra>
    <button
      nz-button
      nzType="primary"
      nzGhost
      (click)="onClosePricingDetails()"
      data-testid="property-details-drawer-close-btn"
    >
      <nz-icon nzType="close-circle" nzTheme="fill" />
      Close
    </button>
  </ng-template>

  <!-- Price Update Modal -->
  <ng-template #confirmModal>
    <div>
      <p>
        Are you sure you want to update the special price to "{{
          selectedRowData?.specialPrice
        }}"?
      </p>
      <div class="d-flex justify-content-end gap-2">
        <button nz-button (click)="handlePriceModalCancel()">Cancel</button>
        <button nz-button nzType="primary" (click)="handlePriceModalSave()">
          Save
        </button>
      </div>
    </div>
  </ng-template>
</div>
