import {
  Component,
  Input,
  Output,
  EventEmitter,
  OnInit,
  OnChanges,
  OnDestroy,
  SimpleChanges,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subscription } from 'rxjs';

import { NzCardModule } from 'ng-zorro-antd/card';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzButtonModule } from 'ng-zorro-antd/button';

import { IUserFields } from '../../../core/interface/user-fields';
import { OnboardingStep } from '../../../core/constants/company';
import { OnboardingState } from '../../../core/interface/company-fields';
import { CompanyState } from '../../../core/components/company-state';

@Component({
  selector: 'app-preview-and-submit-form',
  imports: [CommonModule, NzCardModule, NzIconModule, NzButtonModule],
  templateUrl: './preview-and-submit-form.component.html',
  styleUrl: './preview-and-submit-form.component.scss',
})
export class PreviewAndSubmitFormComponent
  implements OnInit, OnChanges, OnDestroy
{
  @Input() tableData: IUserFields[] = [];
  @Input() isLoading = false;

  @Output() editSection = new EventEmitter<OnboardingStep>();
  @Output() createCompany = new EventEmitter<void>();

  OnboardingStep = OnboardingStep;
  onboardingState: OnboardingState | null = null;
  private subscription: Subscription = new Subscription();

  isCreateButtonEnabled = false;

  constructor(private onboardingStateService: CompanyState) {}

  ngOnInit(): void {
    this.onboardingState =
      this.onboardingStateService.getCurrentOnboardingState();

    this.subscription.add(
      this.onboardingStateService.onboardingState$.subscribe(
        (state: OnboardingState) => {
          this.onboardingState = state;
          this.updateCreateButtonState();
        },
      ),
    );

    setTimeout(() => {
      this.updateCreateButtonState();
    }, 100);

    this.updateCreateButtonState();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['tableData']) {
      this.onboardingState =
        this.onboardingStateService.getCurrentOnboardingState();
      this.updateCreateButtonState();
    }
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  onEditSection(step: OnboardingStep): void {
    this.editSection.emit(step);
  }

  onCreateCompany(): void {
    this.onboardingState =
      this.onboardingStateService.getCurrentOnboardingState();
    this.createCompany.emit();
  }

  updateCreateButtonState(): void {
    this.onboardingState =
      this.onboardingStateService.getCurrentOnboardingState();

    const companyDetails = this.onboardingState?.companyDetails;
    const billingDetails = this.onboardingState?.billingDetails;
    const hasUsers = this.tableData && this.tableData.length > 0;

    const companyValidation = {
      hasCompanyDetails: !!companyDetails,
      hasName: !!companyDetails?.name?.trim(),
      hasAbn: !!companyDetails?.abn?.trim(),
      hasAcn: !!companyDetails?.acn?.trim(),
      hasBillingEmail: !!companyDetails?.billingEmail?.trim(),
      isEmailValid: companyDetails?.billingEmail
        ? this.isValidEmail(companyDetails.billingEmail)
        : false,
      hasAddressLine1: !!companyDetails?.addressLine1?.trim(),
      hasState: this.isValidValue(companyDetails?.state),
      hasPostalCode: this.isValidValue(companyDetails?.postalCode),
    };

    const isCompanyValid =
      companyValidation.hasCompanyDetails &&
      companyValidation.hasName &&
      companyValidation.hasAbn &&
      companyValidation.hasAcn &&
      companyValidation.hasBillingEmail &&
      companyValidation.isEmailValid &&
      companyValidation.hasAddressLine1 &&
      companyValidation.hasState &&
      companyValidation.hasPostalCode;

    let isBillingValid = false;

    if (billingDetails) {
      if (billingDetails.sameAsCompanyDetails) {
        isBillingValid = true;
      } else {
        const billingValidation = {
          hasAbn: !!billingDetails.abn?.trim(),
          hasAcn: !!billingDetails.acn?.trim(),
          hasAddressLine1: !!billingDetails.addressLine1?.trim(),
          hasState: this.isValidValue(billingDetails.state),
          hasPostalCode: this.isValidValue(billingDetails.postalCode),
          isEmailValid:
            !billingDetails.billingEmail?.trim() ||
            this.isValidEmail(billingDetails.billingEmail),
        };

        isBillingValid =
          billingValidation.hasAbn &&
          billingValidation.hasAcn &&
          billingValidation.hasAddressLine1 &&
          billingValidation.hasState &&
          billingValidation.hasPostalCode &&
          billingValidation.isEmailValid;
      }
    }

    this.isCreateButtonEnabled = !!(
      isCompanyValid &&
      isBillingValid &&
      hasUsers
    );

    if (hasUsers && companyDetails && billingDetails) {
      this.isCreateButtonEnabled = true;
      return;
    }
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  private isValidValue(value: string | number | null | undefined): boolean {
    if (value === null || value === undefined) return false;
    if (typeof value === 'string')
      return value.trim() !== '' && value.trim() !== '0';
    if (typeof value === 'number') return value > 0;
    return !!value;
  }
}
