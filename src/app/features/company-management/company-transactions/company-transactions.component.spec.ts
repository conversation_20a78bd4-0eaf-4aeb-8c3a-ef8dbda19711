import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CompanyTransactionsComponent } from './company-transactions.component';
import { Component, Input } from '@angular/core';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ActivatedRoute } from '@angular/router';
import { By } from '@angular/platform-browser';

// Mock PurchasesComponent
@Component({
  selector: 'app-purchases',
  standalone: true,
  template: '',
})
class MockPurchasesComponent {
  @Input() companyId?: number;
}

describe('CompanyTransactionsComponent', () => {
  let component: CompanyTransactionsComponent;
  let fixture: ComponentFixture<CompanyTransactionsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        CompanyTransactionsComponent,
        MockPurchasesComponent,
        HttpClientTestingModule,
      ],
      providers: [
        {
          provide: ActivatedRoute,
          useValue: { snapshot: { paramMap: { get: () => null } } },
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(CompanyTransactionsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should pass companyId to app-purchases component', () => {
    const testCompanyId = 123;
    component.companyId = testCompanyId;
    fixture.detectChanges();

    const purchasesDebugElement = fixture.debugElement.query(
      By.css('app-purchases'),
    );
    expect(purchasesDebugElement).toBeTruthy(
      'app-purchases element should be present',
    );

    const purchasesComponent =
      purchasesDebugElement.componentInstance as MockPurchasesComponent;
    expect(purchasesComponent.companyId).toBe(
      testCompanyId,
      'companyId should be passed to app-purchases',
    );
  });
});
