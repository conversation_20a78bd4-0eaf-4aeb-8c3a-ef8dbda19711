import { ComponentFixture, TestBed } from '@angular/core/testing';
import { provideHttpClient } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { of } from 'rxjs';

import { ActiveInactiveUsersComponent } from './active-inactive-users.component';
import { NotificationService } from '../../../core/services/notification.service';
import { RegisterService } from '../../../core/services/register.service';
import { NzModalService } from 'ng-zorro-antd/modal';

describe('ActiveInactiveUsersComponent', () => {
  let component: ActiveInactiveUsersComponent;
  let fixture: ComponentFixture<ActiveInactiveUsersComponent>;
  let mockNotificationService: jasmine.SpyObj<NotificationService>;
  let mockRegisterService: jasmine.SpyObj<RegisterService>;
  let mockModalService: jasmine.SpyObj<NzModalService>;

  beforeEach(async () => {
    mockNotificationService = jasmine.createSpyObj('NotificationService', [
      'error',
      'success',
      'warning',
    ]);
    mockRegisterService = jasmine.createSpyObj('RegisterService', [
      'getAllUsers',
    ]);
    mockModalService = jasmine.createSpyObj('NzModalService', ['confirm']);

    await TestBed.configureTestingModule({
      imports: [ActiveInactiveUsersComponent, NoopAnimationsModule],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        { provide: NotificationService, useValue: mockNotificationService },
        { provide: RegisterService, useValue: mockRegisterService },
        { provide: NzModalService, useValue: mockModalService },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(ActiveInactiveUsersComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.isLoading).toBeFalse();
    expect(component.isActiveUsersAccordianOpen).toBeTrue();
    expect(component.isDeletedUsersAccordionOpen).toBeFalse();
    expect(component.isAddUserDrawerOpen).toBeFalse();
    expect(component.userTableData).toEqual([]);
    expect(component.deletedUserTableData).toEqual([]);
  });

  it('should toggle active users accordion', () => {
    const initialState = component.isActiveUsersAccordianOpen;
    component.toggleActiveUsersAccordian();
    expect(component.isActiveUsersAccordianOpen).toBe(!initialState);
  });

  it('should toggle deleted users accordion', () => {
    const initialState = component.isDeletedUsersAccordionOpen;
    component.toggleDeletedUsersAccordion();
    expect(component.isDeletedUsersAccordionOpen).toBe(!initialState);
  });

  it('should open and close add user drawer', () => {
    component.openAddUserDrawer();
    expect(component.isAddUserDrawerOpen).toBeTrue();

    component.closeAddUserDrawer();
    expect(component.isAddUserDrawerOpen).toBeFalse();
  });

  it('should handle user added event', () => {
    const mockUser = {
      id: 1001,
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
    };
    spyOn(component, 'getUsers');
    spyOn(component.userUpdated, 'emit');

    component.onUserAdded(mockUser);

    expect(component.userTableData[0]).toEqual(mockUser);
    expect(component.isAddUserDrawerOpen).toBeFalse();
    expect(component.getUsers).toHaveBeenCalled();
    expect(component.userUpdated.emit).toHaveBeenCalled();
  });

  it('should get users when companyId is provided', () => {
    component.companyId = 1;
    mockRegisterService.getAllUsers.and.returnValue(
      of({ success: true, data: { content: [] } }),
    );

    component.getUsers();

    expect(mockRegisterService.getAllUsers).toHaveBeenCalledWith(1);
  });

  it('should show error when companyId is not available', () => {
    component.companyId = undefined;

    component.getUsers();

    expect(mockNotificationService.error).toHaveBeenCalled();
  });
});
