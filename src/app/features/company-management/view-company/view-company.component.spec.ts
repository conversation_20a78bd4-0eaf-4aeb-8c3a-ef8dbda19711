import {
  ComponentFixture,
  TestBed,
  fakeAsync,
  tick,
} from '@angular/core/testing';
import { ViewCompanyComponent } from './view-company.component';
import { ActivatedRoute, Router } from '@angular/router';
import { CompanyState } from '../../../core/components/company-state';
import { NotificationService } from '../../../core/services/notification.service';
import { NzBreadCrumbModule } from 'ng-zorro-antd/breadcrumb';
import { NzTabsModule } from 'ng-zorro-antd/tabs';
import { NzSwitchModule } from 'ng-zorro-antd/switch';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { provideAnimations } from '@angular/platform-browser/animations';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { AddressResponseDTO, CompanyDTO } from '../../../api-client';
import { ICompanyFields } from 'src/app/core/interface/company-fields';

describe('ViewCompanyComponent', () => {
  let component: ViewCompanyComponent;
  let fixture: ComponentFixture<ViewCompanyComponent>;
  let mockCompanyState: jasmine.SpyObj<CompanyState>;
  let mockRouter: jasmine.SpyObj<Router>;
  let mockNotification: jasmine.SpyObj<NotificationService>;

  const mockCompany: ICompanyFields = {
    id: 1,
    name: 'Test Company',
    abn: '*********',
    acn: '*********',
    accountsContactName: 'John Doe',
    billingEmail: '<EMAIL>',
    isActive: true,
    primaryAddress: {
      addressLine1: '123 Main St',
      suburb: 'Suburb',
      stateName: 'State',
      zipCode: '12345',
      addressType: AddressResponseDTO.AddressTypeEnum.Primary,
    },
    billingAddress: {
      addressLine1: '456 Billing St',
      suburb: 'Suburb',
      stateName: 'State',
      zipCode: '54321',
      addressType: AddressResponseDTO.AddressTypeEnum.Billing,
    },
  };

  beforeEach(async () => {
    mockCompanyState = jasmine.createSpyObj<CompanyState>('CompanyState', [
      'getCompanyData',
      'setCompanyData',
      'clearCompanyData',
      'toggleCompanyStatus',
      'clearTempCompanyData',
    ]);
    mockRouter = jasmine.createSpyObj<Router>('Router', ['navigate']);
    mockNotification = jasmine.createSpyObj<NotificationService>(
      'NotificationService',
      ['success', 'error'],
    );

    await TestBed.configureTestingModule({
      imports: [
        ViewCompanyComponent,
        NzBreadCrumbModule,
        NzTabsModule,
        NzSwitchModule,
        NzIconModule,
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        HttpClientTestingModule,
      ],
      providers: [
        {
          provide: ActivatedRoute,
          useValue: { snapshot: { paramMap: { get: () => '1' } } },
        },
        { provide: CompanyState, useValue: mockCompanyState },
        { provide: Router, useValue: mockRouter },
        { provide: NotificationService, useValue: mockNotification },
        provideAnimations(),
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(ViewCompanyComponent);
    component = fixture.componentInstance;
    mockCompanyState.getCompanyData.and.returnValue(mockCompany);
  });

  it('should create component', () => {
    fixture.detectChanges();
    expect(component).toBeTruthy();
  });

  it('should initialize with company data', fakeAsync(() => {
    component.ngOnInit();
    tick();
    expect(component.companyId).toBe(1);
    expect(component.company).toEqual(mockCompany);
  }));

  it('should navigate to company list if no companyId', async () => {
    TestBed.resetTestingModule();
    await TestBed.configureTestingModule({
      imports: [
        ViewCompanyComponent,
        NzBreadCrumbModule,
        NzTabsModule,
        NzSwitchModule,
        NzIconModule,
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        HttpClientTestingModule,
      ],
      providers: [
        {
          provide: ActivatedRoute,
          useValue: { snapshot: { paramMap: { get: () => null } } },
        },
        { provide: CompanyState, useValue: mockCompanyState },
        { provide: Router, useValue: mockRouter },
        { provide: NotificationService, useValue: mockNotification },
        provideAnimations(),
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(ViewCompanyComponent);
    component = fixture.componentInstance;
    component.ngOnInit();
    fixture.detectChanges();

    expect(mockRouter.navigate).toHaveBeenCalledWith(['company']);
  });

  it('should format primary address', fakeAsync(() => {
    component.ngOnInit();
    tick();
    fixture.detectChanges();
    expect(component.primaryAddressFormatted).toBe(
      '123 Main St, Suburb, State, 12345',
    );
  }));

  it('should format billing address', fakeAsync(() => {
    component.ngOnInit();
    tick();
    fixture.detectChanges();
    expect(component.billingAddressFormatted).toBe(
      '456 Billing St, Suburb, State, 54321',
    );
  }));

  it('should show edit form when editCompany called', fakeAsync(() => {
    component.ngOnInit();
    tick();
    fixture.detectChanges();
    component.editCompany();
    expect(component.isEditFormVisible).toBeTrue();
  }));

  it('should toggle company status', fakeAsync(() => {
    mockCompanyState.toggleCompanyStatus.and.callFake((company, successCb) => {
      successCb();
    });
    component.ngOnInit();
    tick();
    fixture.detectChanges();
    component.company = mockCompany;
    component.toggleActive();
    tick();
    expect(mockCompanyState.toggleCompanyStatus).toHaveBeenCalled();
    expect(mockNotification.success).toHaveBeenCalledWith(
      'Company status updated to active',
    );
  }));

  it('should handle company saved event', fakeAsync(() => {
    const updatedCompany: CompanyDTO = {
      id: 1,
      name: 'Updated Company',
      abn: '*********',
      acn: '*********',
      accountsContactName: 'John Doe',
      billingEmail: '<EMAIL>',
      isActive: true,
      primaryAddress: {
        addressLine1: '123 Main St',
        suburb: 'Suburb',
        stateName: 'State',
        zipCode: '12345',
        addressType: AddressResponseDTO.AddressTypeEnum.Primary,
      },
      billingAddress: {
        addressLine1: '456 Billing St',
        suburb: 'Suburb',
        stateName: 'State',
        zipCode: '54321',
        addressType: AddressResponseDTO.AddressTypeEnum.Billing,
      },
    };
    component.ngOnInit();
    tick();
    fixture.detectChanges();
    component.company = mockCompany;
    component.isEditFormVisible = true;
    component.onCompanySaved(updatedCompany);
    fixture.detectChanges();
    expect(component.company?.name).toBe('Updated Company');
    expect(mockCompanyState.setCompanyData).toHaveBeenCalledWith(
      component.company,
    );
    expect(component.isEditFormVisible).toBeFalse();
  }));

  it('should handle form cancellation', fakeAsync(() => {
    component.ngOnInit();
    tick();
    fixture.detectChanges();
    component.isEditFormVisible = true;
    component.onFormCancelled();
    fixture.detectChanges();
    expect(component.isEditFormVisible).toBeFalse();
    expect(mockCompanyState.clearTempCompanyData).toHaveBeenCalled();
  }));

  it('should navigate back to company list', fakeAsync(() => {
    component.ngOnInit();
    tick();
    fixture.detectChanges();
    component.goBack();
    expect(mockCompanyState.clearCompanyData).toHaveBeenCalled();
    expect(mockRouter.navigate).toHaveBeenCalledWith(['company']);
  }));
});
