import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { DomSanitizer, SafeUrl } from '@angular/platform-browser';
import { Observable, of, Subscription } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { NzBreadCrumbModule } from 'ng-zorro-antd/breadcrumb';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzDrawerModule } from 'ng-zorro-antd/drawer';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzTabsModule } from 'ng-zorro-antd/tabs';
import { NzUploadModule } from 'ng-zorro-antd/upload';
import { RoleDTO, UserRequestDTO, UserResponseDTO } from '../../../api-client';
import { PHONE_REGEX, COMMON_STRINGS } from '../../../core/constants/common';
import { IUserFields } from '../../../core/interface/user-fields';
import { NotificationService } from '../../../core/services/notification.service';
import { RegisterService } from '../../../core/services/register.service';
import { CompanyState } from '../../../core/components/company-state';
import { SharedLookup } from '../../../core/components/shared-lookup';
import {
  UserCreation,
  UserCreationRequest,
  UploadResult,
} from '../../../core/components/user-creation';
import { UserRequestMapper } from '../../../core/components/user-form';
import { ICompanyFields } from '../../../core/interface/company-fields';

@Component({
  selector: 'app-add-user',
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NzTabsModule,
    NzFormModule,
    NzInputModule,
    NzButtonModule,
    NzGridModule,
    NzCheckboxModule,
    NzCardModule,
    NzTableModule,
    NzModalModule,
    NzUploadModule,
    NzIconModule,
    NzDrawerModule,
    NzSelectModule,
    NzBreadCrumbModule,
  ],
  templateUrl: './add-user.component.html',
  styleUrl: './add-user.component.scss',
})
export class AddUserComponent implements OnInit, OnDestroy {
  @Input() isVisible = false;
  @Input() companyId: number | undefined;
  @Input() company: ICompanyFields | null | undefined = null;
  @Input() existingUsers: IUserFields[] = [];
  @Output() userAdded = new EventEmitter<IUserFields>();
  @Output() userUpdated = new EventEmitter<IUserFields>();
  @Output() drawerClosed = new EventEmitter<void>();
  @Input() showBreadcrumbs = false;

  @Input() set userToEdit(value: IUserFields | undefined) {
    this._userToEdit = value;
    if (value) {
      this.isEditMode = true;
      this.populateFormWithUserData();
    } else {
      this.isEditMode = false;
      this.addUserForm.reset();
    }
  }
  get userToEdit(): IUserFields | undefined {
    return this._userToEdit;
  }

  addUserForm: FormGroup;
  isLoading = false;
  roles$: Observable<RoleDTO[]> = of([]);
  roles: RoleDTO[] = [];
  selectedFile: File | null = null;
  isEditMode = false;
  private fileSelectedSubscription: Subscription = new Subscription();
  private originalFormValue?: IUserFields;
  private _userToEdit?: IUserFields;

  constructor(
    private fb: FormBuilder,
    private notification: NotificationService,
    private sharedLookupService: SharedLookup,
    private companyStateService: CompanyState,
    private userCreationService: UserCreation,
    private registerService: RegisterService,
    private userRequestMapper: UserRequestMapper,
    private sanitizer: DomSanitizer,
  ) {
    this.addUserForm = this.fb.group({
      firstName: ['', Validators.required],
      lastName: ['', Validators.required],
      email: ['', [Validators.required, Validators.email]],
      contactNumber: [
        '',
        [Validators.required, Validators.pattern(PHONE_REGEX)],
      ],
      roleId: [null, Validators.required],
      profilePictureUrl: [null],
    });
  }

  get profileImageSrc(): SafeUrl | string {
    const profilePictureUrl = this.addUserForm.get('profilePictureUrl')?.value;
    if (profilePictureUrl && profilePictureUrl.startsWith('data:image')) {
      return this.sanitizer.bypassSecurityTrustUrl(profilePictureUrl);
    }
    return profilePictureUrl || 'assets/profileImage.png';
  }

  ngOnInit(): void {
    this.fetchRoles();
    if (this.userToEdit) {
      this.isEditMode = true;
      this.populateFormWithUserData();
    }
    this.addUserForm.valueChanges.subscribe(() => {
      this.checkFormChanges();
    });
  }

  ngOnDestroy(): void {
    if (this.fileSelectedSubscription) {
      this.fileSelectedSubscription.unsubscribe();
    }
  }

  private populateFormWithUserData(): void {
    if (!this.userToEdit) {
      console.error('AddUserComponent: No userToEdit provided for population');
      return;
    }
    this.addUserForm.patchValue({
      firstName: this.userToEdit.firstName || '',
      lastName: this.userToEdit.lastName || '',
      email: this.userToEdit.email || '',
      contactNumber: this.userToEdit.contactNumber || '',
      roleId: this.userToEdit.roleId || null,
      profilePictureUrl: this.userToEdit.profilePictureUrl || null,
    });
    this.originalFormValue = { ...this.addUserForm.value };
    this.addUserForm.markAsPristine();
  }

  private checkFormChanges(): void {
    if (this.isEditMode && this.originalFormValue) {
      const currentValue: IUserFields = this.addUserForm.value;
      const hasChanges =
        Object.keys(this.originalFormValue).some(
          (key) => currentValue[key] !== this.originalFormValue![key],
        ) || this.selectedFile !== null;
      if (!hasChanges) {
        this.addUserForm.markAsPristine();
      } else {
        this.addUserForm.markAsDirty();
      }
    }
  }

  toggleAddUserDrawer(): void {
    if (this.isVisible) {
      this.drawerClosed.emit();
    }
    this.addUserForm.reset();
    this.selectedFile = null;
    this.isEditMode = false;
    this.originalFormValue = undefined;
  }

  fetchRoles(): Observable<RoleDTO[]> {
    this.isLoading = true;
    this.roles$ = this.sharedLookupService.fetchRoles().pipe(
      map((roles: RoleDTO[]) => {
        this.isLoading = false;
        this.roles = roles;
        return roles;
      }),
      catchError(() => {
        this.isLoading = false;
        this.notification.error(
          COMMON_STRINGS.errorMessages.displayRolesFailed,
        );
        return of([]);
      }),
    );
    return this.roles$;
  }

  submitForm(): void {
    if (!this.isAddUserFormValid()) {
      this.handleInvalidAddUserForm();
      return;
    }
    this.isLoading = true;
    if (this.isEditMode) {
      this.updateUser();
    } else {
      this.addUser();
    }
  }

  private addUser(): void {
    const request: UserCreationRequest = {
      formData: this.addUserForm.value,
      companyId: this.companyId!,
      company: this.company || undefined,
      selectedFile: this.selectedFile,
      userType: 'COMPANY',
    };

    this.userCreationService.createUserWithProfilePicture(request).subscribe({
      next: (result) => {
        if (result.success && result.userData) {
          this.handleCreateUserSuccess(result.userData);
          this.selectedFile = null;
          this.addUserForm.reset();
        }
      },
      error: (error) => {
        console.error('Error creating user:', error);
        this.notification.error(
          COMMON_STRINGS.warningMessages.userCreationFailed,
        );
        this.isLoading = false;
      },
      complete: () => {
        this.isLoading = false;
      },
    });
  }

  private updateUser(): void {
    const userToEdit: IUserFields | undefined = this.userToEdit;
    if (!userToEdit?.id) {
      this.notification.error(COMMON_STRINGS.errorMessages.userIdNotFound);
      this.isLoading = false;
      return;
    }

    const userRequest = this.userRequestMapper.createUserRequest(
      this.addUserForm,
      this.company,
      this.companyId,
      userToEdit.isActive,
    );

    if (!userRequest) {
      this.notification.error(
        COMMON_STRINGS.errorMessages.companyAddressRequired,
      );
      this.isLoading = false;
      return;
    }

    if (this.selectedFile) {
      this.userCreationService
        .uploadProfilePicture(userRequest.email, this.selectedFile)
        .subscribe({
          next: (result: UploadResult) => {
            if (result.success && result.url) {
              userRequest.profilePictureUrl = result.url;
              this.performUserUpdate(userToEdit.id!, userRequest);
            } else {
              this.notification.error(
                result.error || 'Failed to upload profile picture',
              );
              this.isLoading = false;
            }
          },
          error: (error) => {
            console.error('Error uploading profile picture:', error);
            this.notification.error(
              COMMON_STRINGS.errorMessages.failedToUploadProfilePicture,
            );
            this.isLoading = false;
          },
        });
    } else {
      this.performUserUpdate(userToEdit.id!, userRequest);
    }
  }

  private performUserUpdate(userId: number, userRequest: UserRequestDTO): void {
    this.registerService.updateUser(userId, userRequest).subscribe({
      next: (response) => {
        if (response.success && response.data) {
          const updatedUser =
            this.userRequestMapper.mapUserResponseToUserFields(
              response.data,
              this.roles,
              this.companyStateService.getCompanyDetails(),
            );
          if (updatedUser) {
            this.userUpdated.emit(updatedUser);
            this.notification.success(
              COMMON_STRINGS.successMessages.userUpdateSuccess,
            );
          }
        }
        this.isLoading = false;
        this.drawerClosed.emit();
      },
      error: (error) => {
        console.error('Error updating user:', error);
        this.notification.error(
          COMMON_STRINGS.errorMessages.failedToUpdateUser,
        );
        this.isLoading = false;
      },
    });
  }

  private isAddUserFormValid(): boolean {
    if (this.companyId == null && !this.isEditMode) {
      return false;
    }
    return this.addUserForm.valid;
  }

  private handleInvalidAddUserForm(): void {
    this.addUserForm.markAllAsTouched();
    this.notification.error(COMMON_STRINGS.warningMessages.enterAllFields);
  }

  private handleCreateUserSuccess(userData: UserResponseDTO): void {
    if (!userData) {
      this.handleCreateUserError();
      return;
    }

    const updatedUser = this.userRequestMapper.mapUserResponseToUserFields(
      userData,
      this.roles,
      this.company,
    );

    if (!updatedUser) {
      this.handleCreateUserError();
      return;
    }

    this.existingUsers.unshift(updatedUser);
    this.addUserForm.reset();
    this.toggleAddUserDrawer();
    this.isLoading = false;
    this.userAdded.emit(updatedUser);
  }

  private handleCreateUserError(): void {
    this.notification.error(COMMON_STRINGS.warningMessages.userCreationFailed);
    this.isLoading = false;
  }

  getErrorTip(controlName: string): string | undefined {
    const control = this.addUserForm.get(controlName);
    if (control?.touched && control?.hasError('required')) {
      return `${controlName.charAt(0).toUpperCase() + controlName.slice(1)} is required`;
    }
    if (
      controlName === 'email' &&
      control?.touched &&
      control?.hasError('email')
    ) {
      return COMMON_STRINGS.warningMessages.enterValidEmail;
    }
    if (
      controlName === 'contactNumber' &&
      control?.touched &&
      control?.hasError('pattern')
    ) {
      return COMMON_STRINGS.warningMessages.enterValidPhoneNumber;
    }
    return undefined;
  }

  triggerFileInput(index: number): void {
    this.companyStateService.triggerFileInput(index);
  }

  onFileSelected(event: Event): void {
    this.userCreationService.handleFileSelection(
      event,
      (file: File, dataUrl: string) => {
        this.selectedFile = file;
        this.addUserForm.get('profilePictureUrl')?.setValue(dataUrl);
        this.addUserForm.markAsDirty();
        this.companyStateService.fileUploadEvent(event);
      },
    );
  }
  isUserFormValid(): boolean {
    if (this.isEditMode) {
      return (
        this.addUserForm.valid &&
        (this.addUserForm.dirty || this.selectedFile !== null)
      );
    }
    const email = this.addUserForm.get('email')?.value;
    return this.addUserForm.valid && (!email || this.isEmailUnique(email));
  }

  isEmailUnique(email: string): boolean {
    return !this.existingUsers.some(
      (user) => user.email?.toLowerCase() === email.toLowerCase(),
    );
  }

  onImageError(event: Event): void {
    const imgElement = event.target as HTMLImageElement;
    imgElement.src = 'assets/profileImage.png';
    imgElement.alt = 'Default profile image';
  }

  navigateToManageUsers(): void {
    this.toggleAddUserDrawer();
  }
}
