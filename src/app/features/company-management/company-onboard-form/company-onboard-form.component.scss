.company-form-wrapper {
  display: flex;
  justify-content: center;
}

.company-form {
  position: relative;
  width: 100%;
  padding: 20px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
  background: #E8F3FE;
}

.left-column {
  padding-right: 75px !important;
  padding-left: 130px !important;
}

.right-column {
  padding-left: 75px !important;
  padding-right: 130px !important;
}

.nz-form-field {
  height: 45px;
}

input.is-invalid {
  background-image: none !important;
}

.text-danger {
  font-size: 12px;
  margin-top: 5px;
  color: #ff4d4f;
}

:host ::ng-deep .ant-form-item-has-error .ant-input {
  border-color: #ff4d4f !important;
}

:host ::ng-deep .ant-form-item-explain-error {
  color: #ff4d4f;
}

::ng-deep .ant-card-bordered {
  background: #ffffff;
}

.billing-form {
  .company-form {
    background: #f0f8ff;
  }
}

.billing-checkbox {
  margin-bottom: 20px;
  padding-left: 120px;
}

.checkbox-label {
  font-weight: bold;
  color: var(--primary-button-color);
}
