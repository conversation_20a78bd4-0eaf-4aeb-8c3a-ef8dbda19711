<!-- Save/Cancel Buttons -->
<div class="form-buttons d-flex justify-content-end mb-3">
  <button
    nz-button
    nzType="primary"
    type="button"
    (click)="saveCompanyDetails()"
    [disabled]="!isSaveButtonEnabled"
  >
    {{ isLoading ? 'Saving...' : 'Save' }}
  </button>
  <button
    nz-button
    nzType="default"
    type="button"
    (click)="cancelEdit()"
    [disabled]="isLoading"
  >
    Cancel
  </button>
</div>

<form nz-form nzLayout="vertical" [formGroup]="companyForm">
  <div class="row company-edit-form">
    <div class="col-md-6">
      <nz-form-item class="mb-3 left-company-edit-form-fields">
        <nz-form-label [nzRequired]="true" class="form-label"
          >Company Name</nz-form-label
        >
        <nz-form-control [nzErrorTip]="getCompanyErrorTip('name')">
          <input
            class="input-company-field form-field"
            nz-input
            placeholder="Enter company name"
            formControlName="name"
          />
        </nz-form-control>
      </nz-form-item>
      <nz-form-item class="mb-3 left-company-edit-form-fields">
        <nz-form-label [nzRequired]="true" class="form-label"
          >ABN</nz-form-label
        >
        <nz-form-control [nzErrorTip]="getCompanyErrorTip('abn')">
          <input
            class="input-company-field form-field"
            nz-input
            placeholder="Enter ABN"
            formControlName="abn"
          />
        </nz-form-control>
      </nz-form-item>
      <nz-form-item class="mb-3 left-company-edit-form-fields">
        <nz-form-label [nzRequired]="true" class="form-label"
          >ACN</nz-form-label
        >
        <nz-form-control [nzErrorTip]="getCompanyErrorTip('acn')">
          <input
            class="input-company-field form-field"
            nz-input
            placeholder="Enter ACN"
            formControlName="acn"
          />
        </nz-form-control>
      </nz-form-item>
      <nz-form-item class="mb-3 left-company-edit-form-fields">
        <nz-form-label class="form-label">Accounts Email</nz-form-label>
        <nz-form-control [nzErrorTip]="getCompanyErrorTip('billingEmail')">
          <input
            class="input-company-field form-field"
            nz-input
            placeholder="Enter accounts email"
            formControlName="billingEmail"
          />
        </nz-form-control>
      </nz-form-item>
      <nz-form-item class="mb-3 left-company-edit-form-fields">
        <nz-form-label class="form-label">Key Contact Person</nz-form-label>
        <nz-form-control>
          <input
            class="input-company-field form-field"
            nz-input
            placeholder="Enter contact name"
            formControlName="accountsContactName"
          />
        </nz-form-control>
      </nz-form-item>
      <nz-form-item class="mb-3 left-company-edit-form-fields">
        <nz-form-label class="form-label">Accounts Telephone</nz-form-label>
        <nz-form-control
          [nzErrorTip]="getCompanyErrorTip('accountsContactNumber')"
        >
          <input
            class="input-company-field form-field"
            nz-input
            placeholder="Enter contact number"
            formControlName="accountsContactNumber"
          />
        </nz-form-control>
      </nz-form-item>
    </div>
    <div class="col-md-6">
      <nz-form-item class="mb-3 right-company-edit-form-fields">
        <nz-form-label [nzRequired]="true" class="form-label"
          >Address 1</nz-form-label
        >
        <nz-form-control [nzErrorTip]="getCompanyErrorTip('addressLine1')">
          <input
            class="input-company-field form-field"
            nz-input
            placeholder="Enter address"
            formControlName="addressLine1"
          />
        </nz-form-control>
      </nz-form-item>
      <nz-form-item class="mb-3 right-company-edit-form-fields">
        <nz-form-label class="form-label">Address 2</nz-form-label>
        <nz-form-control>
          <input
            class="input-company-field form-field"
            nz-input
            placeholder="Enter address"
            formControlName="addressLine2"
          />
        </nz-form-control>
      </nz-form-item>
      <nz-form-item class="mb-3 right-company-edit-form-fields">
        <nz-form-label class="form-label">Suburb</nz-form-label>
        <nz-form-control>
          <input
            class="input-company-field form-field"
            nz-input
            placeholder="Enter suburb"
            formControlName="suburb"
          />
        </nz-form-control>
      </nz-form-item>
      <nz-form-item class="mb-3 right-company-edit-form-fields">
        <nz-form-label [nzRequired]="true" class="form-label"
          >State</nz-form-label
        >
        <nz-form-control [nzErrorTip]="getCompanyErrorTip('state')">
          <nz-select
            class="input-company-field form-field"
            placeholder="Select state"
            formControlName="state"
          >
            <nz-option
              *ngFor="let state of states"
              [nzValue]="state.id"
              [nzLabel]="state.stateName || ''"
            ></nz-option>
          </nz-select>
        </nz-form-control>
      </nz-form-item>
      <nz-form-item class="mb-3 right-company-edit-form-fields">
        <nz-form-label [nzRequired]="true" class="form-label"
          >Zipcode</nz-form-label
        >
        <nz-form-control [nzErrorTip]="getCompanyErrorTip('postalCode')">
          <nz-select
            class="input-company-field form-field"
            placeholder="Select postal code"
            formControlName="postalCode"
            [nzDisabled]="!companyForm.get('state')?.value"
          >
            <nz-option
              *ngFor="let zipcode of postalCode"
              [nzValue]="zipcode.id"
              [nzLabel]="zipcode.zipCode || ''"
            ></nz-option>
          </nz-select>
        </nz-form-control>
      </nz-form-item>
    </div>
  </div>
</form>

<div
  class="billing-accordian"
  (click)="toggleBillingDetailsAccordion()"
  role="button"
  aria-label="Toggle billing details"
>
  <span>Edit Billing Details</span>
  <i
    class="fa-solid chevron"
    [ngClass]="
      isBillingDetailsAccordionOpen ? 'fa-chevron-right' : 'fa-chevron-up'
    "
  ></i>
</div>

<div *ngIf="isBillingDetailsAccordionOpen" class="accordion-content">
  <div class="edit-form-checkbox">
    <nz-checkbox
      type="checkbox"
      nz-checkbox
      [nzChecked]="isSameAsCompanyDetails"
      (nzCheckedChange)="onSameAsCompanyDetailsChange($event)"
      id="sameAsCompanyDetails"
      data-testid="same-as-company-details-checkbox"
    >
      Same as Company Details
    </nz-checkbox>
  </div>
  <form nz-form nzLayout="vertical" [formGroup]="companyForm">
    <div class="row company-edit-form">
      <div class="col-md-6">
        <nz-form-item class="mb-3 left-company-edit-form-fields">
          <nz-form-label [nzRequired]="true" class="form-label"
            >Address 1</nz-form-label
          >
          <nz-form-control
            [nzErrorTip]="getCompanyErrorTip('billingAddressLine1')"
          >
            <input
              class="input-company-field form-field"
              nz-input
              placeholder="Enter address"
              formControlName="billingAddressLine1"
            />
          </nz-form-control>
        </nz-form-item>
        <nz-form-item class="mb-3 left-company-edit-form-fields">
          <nz-form-label class="form-label">Address 2</nz-form-label>
          <nz-form-control>
            <input
              class="input-company-field form-field"
              nz-input
              placeholder="Enter address"
              formControlName="billingAddressLine2"
            />
          </nz-form-control>
        </nz-form-item>
        <nz-form-item class="mb-3 left-company-edit-form-fields">
          <nz-form-label class="form-label">Suburb</nz-form-label>
          <nz-form-control>
            <input
              class="input-company-field form-field"
              nz-input
              placeholder="Enter suburb"
              formControlName="billingSuburb"
            />
          </nz-form-control>
        </nz-form-item>
      </div>
      <div class="col-md-6">
        <nz-form-item class="mb-3 right-company-edit-form-fields">
          <nz-form-label [nzRequired]="true" class="form-label"
            >State</nz-form-label
          >
          <nz-form-control [nzErrorTip]="getCompanyErrorTip('billingState')">
            <nz-select
              class="input-company-field form-field"
              [nzPlaceHolder]="'Select state'"
              formControlName="billingState"
            >
              <nz-option
                *ngFor="let state of states"
                [nzValue]="state.id"
                [nzLabel]="state.stateName || ''"
              ></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
        <nz-form-item class="mb-3 right-company-edit-form-fields">
          <nz-form-label [nzRequired]="true" class="form-label"
            >Zipcode</nz-form-label
          >
          <nz-form-control
            [nzErrorTip]="getCompanyErrorTip('billingPostalCode')"
          >
            <nz-select
              class="input-company-field form-field"
              [nzPlaceHolder]="'Select postal code'"
              formControlName="billingPostalCode"
              [nzDisabled]="!companyForm.get('billingState')?.value || isSameAsCompanyDetails"
            >
              <nz-option
                *ngFor="let zipcode of billingZipcode"
                [nzValue]="zipcode.id"
                [nzLabel]="zipcode.zipCode || ''"
              ></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </div>
    </div>
  </form>
</div>
