.featured-search-container {
  .tab-wrapper {
    padding: 0 24px;
  }

  .parent-tab-bar {
    display: flex;
    gap: 12px;
    background: #f5f5f5;
    border-radius: 8px;
    margin-top: 15px;
    align-items: center;
    justify-content: space-between;
  }

  .tab {
    padding: 16px 24px;
    font-weight: 500;
    font-size: 16px;
    cursor: pointer;
    color: #666;
    background: #f5f5f5;
    border: none;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin-left: 10px;

    &.active {
      color: #4a90e2;
      font-weight: 600;
    }
  }

  .child-tabs {
    display: inline-flex;
    list-style: none;
    margin: 10px 0 0 0;
    padding: 0 20px;
    background: #f5f5f5;
    border-top: 1px solid #e8e8e8;
    border-radius: 4px;
    align-items: flex-start;
  }

  .child-tabs li {
    font-size: 14px;
    cursor: pointer;
    padding: 12px 16px;
    color: #666;
    background: #f5f5f5;
    transition: all 0.3s;
    position: relative; // Required for underline positioning
  
    &.active {
      color: #4a90e2;
      font-weight: 600;
  
      &::after {
        content: '';
        position: absolute;
        left: 0;
        bottom: 4px; // Adjust to align underline properly
        width: 100%;
        height: 2px;
        background-color: #4a90e2;
        border-radius: 1px;
      }
    }
  }
  
  .search-content {
    background-color: #fff;
    padding: 24px;
    border-radius: 0 0 8px 8px;
    margin: 0 24px;
  }
}
