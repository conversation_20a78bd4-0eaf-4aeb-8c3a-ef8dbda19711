.custom-transaction-card {
  border: none;
  border-radius: 10px;
  background-color: var(--the-sky-blue);

  .header-banner {
    background-color: #1d64b9;
    font-weight: 500;
    font-size: 16px;
    padding-top: 15px !important;
    padding-bottom: 15px !important;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .p-3 {
    font-size: 14px;

    h4 {
      font-size: 14px;
      font-weight: bold;
      margin-bottom: 0.15rem;
    }

    .text-muted {
      font-size: 14px;
    }

    button {
      font-size: 14px;
    }
  }

  .email-btn {
    background: white;
    border-color: #1d64b9;
    color: #1d64b9;
    font-weight: 500;
    font-size: 14px;

    &:hover {
      background: #fafcff;
      border-color: #1d64b9;
    }
  }

  .wrapper {
    background-color: #fafcff;
    padding: 1px;

    app-data-grid {
      font-size: 14px;
    }
  }

  .ant-card-body {
    padding: 0 !important;
  }

  .total {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 2rem;
    padding-top: 0.5rem;

    p {
      font-size: 14px;
    }

    .total-amount {
      color: #000000;
      font-size: 1.2rem;
      font-weight: bold;
      margin-right: 20rem;
    }
  }
}
