<div class="sidebar" [ngClass]="{ collapsed: isSidebarCollapsed() }">
  <!-- Logo -->
  <div class="logo-container">
    <img
      *ngIf="!isSidebarCollapsed()"
      src="assets/arealyticsImg.png"
      alt="Arealytics"
      class="logo"
    />
    <img
      *ngIf="isSidebarCollapsed()"
      src="assets/arealyticsLogo.png"
      alt="Arealytics"
      class="logo-collapsed"
    />
    <span class="logo-text" *ngIf="!isSidebarCollapsed()"></span>
  </div>

  <!-- Main menu items -->
  <div class="menu-container">
    <ul class="menu-list">
      @for (item of menuItems; track $index) {
        <li
          class="menu-item"
          [routerLink]="item.route"
          routerLinkActive="active"
          *ngIf="item.roleIds.includes(userRoleId)"
        >
          <div class="menu-item-content">
            <div class="menu-item-left">
              <i class="fa fa-{{ item.icon }}" *ngIf="item.icon"></i>
              <span [ngClass]="{ hidden: isSidebarCollapsed() }">{{
                item.label
              }}</span>
            </div>
          </div>
        </li>
      }
    </ul>
  </div>

  <!-- User profile and footer items -->
  <div class="footer-container">
    <!-- User profile -->
    <div
      class="user-profile active"
      *ngIf="isLoggedIn"
      (click)="openProfileDrawer()"
    >
      <ng-container *ngIf="profileImageUrl; else showInitial">
        <img [src]="profileImageUrl" class="avatar" alt="User Avatar" />
      </ng-container>
      <ng-template #showInitial>
        <div class="avatar avatar-fallback">
          {{ (username || email || '')[0].toUpperCase() }}
        </div>
      </ng-template>

      <div class="user-info active">
        <div class="username">{{ username }}</div>
        <div class="email">{{ email }}</div>
      </div>
    </div>
    <!-- Add this divider -->
    <div class="sidebar-divider"></div>

    <!-- Footer menu items -->
    <ul class="footer-menu">
      <li
        *ngFor="let item of footerItems"
        class="footer-item"
        [routerLink]="item.route ? item.route : null"
        (click)="item.action ? item.action() : null"
      >
        <div class="menu-item-content">
          <div class="menu-item-left">
            <i class="fa fa-{{ item.icon }}" *ngIf="item.icon"></i>
            <span>{{ item.label }}</span>
          </div>
          <i class="fa fa-chevron-right arrow-icon" *ngIf="item.hasArrow"></i>
        </div>
      </li>
    </ul>
  </div>
  <!-- Collapse button -->
  <button
    class="collapse-button"
    [ngClass]="{ collapsed: isSidebarCollapsed() }"
    data-testid="sidebar-collapse-btn"
    (click)="toggleSidebar()"
  >
    <i
      class="fa"
      [ngClass]="isSidebarCollapsed() ? 'fa-angle-right' : 'fa-angle-left'"
    ></i>
  </button>
</div>

<nz-drawer
  [nzClosable]="false"
  [nzVisible]="profileDrawerVisible"
  nzPlacement="right"
  [nzTitle]="myProfile"
  [nzExtra]="extraForProfile"
  (nzOnClose)="closeProfileDrawer()"
>
  <ng-container *nzDrawerContent>
    <app-my-profile
      [(visibleProfileDrawer)]="profileDrawerVisible"
      (visibleProfileAfterDelay)="visibleProfileAfterDelay($event)"
    ></app-my-profile>
  </ng-container>
</nz-drawer>

<ng-template #myProfile>
  <h2>My Profile</h2>
</ng-template>

<ng-template #extraForProfile>
  <button nz-button nzType="primary" nzGhost (click)="closeProfileDrawer()">
    <nz-icon
      data-testid="my-profile-close-drawer"
      nzType="close-circle"
      nzTheme="fill"
    />
    Close
  </button>
</ng-template>
