import { Component, HostListener, input, Input, output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzAvatarModule } from 'ng-zorro-antd/avatar';
import { NzDrawerModule } from 'ng-zorro-antd/drawer';
import { NzButtonModule } from 'ng-zorro-antd/button';

import { KeycloakService } from '../../core/services/keycloak.service';
import { UserService } from '../../core/services/user.service';
import { EnumUserRole } from '../../core/enumerations/user-roles';
import { ISidebarItem } from '../../core/interface/sidebar';
import { SIDEBAR_MENU_ITEMS } from '../../core/constants/sidebar';
import { MyProfileComponent } from '../../core/features/my-profile/my-profile.component';

@Component({
  selector: 'app-sidebar',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    NzIconModule,
    NzInputModule,
    NzAvatarModule,
    NzDrawerModule,
    NzButtonModule,
    MyProfileComponent,
  ],
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.scss'],
})
export class SidebarComponent {
  @Input() username = '';
  @Input() email = '';
  @Input() isLoggedIn = false;
  @Input() profileImageUrl: string | null = null;

  isSidebarCollapsed = input(window.innerWidth < 1000);
  isSidebarCollapsedChange = output<boolean>();
  profileDrawerVisible = false;

  menuItems: ISidebarItem[] = SIDEBAR_MENU_ITEMS;

  constructor(
    private userService: UserService,
    private keycloakService: KeycloakService,
  ) {}

  footerItems: ISidebarItem[] = [
    {
      label: 'Logout',
      icon: 'sign-out',
      action: () => this.logout(),
      roleIds: Object.values(EnumUserRole) as EnumUserRole[],
    },
    {
      label: 'Need Help?',
      icon: 'question-circle',
      route: 'help',
      roleIds: Object.values(EnumUserRole).filter(
        (role) => role != EnumUserRole.PLATFORM_ADMIN,
      ) as EnumUserRole[],
    },
  ];

  logout(): void {
    this.keycloakService.logout();
    this.userService.removeUserInfo();
  }

  toggleSidebar(): void {
    this.isSidebarCollapsedChange.emit(!this.isSidebarCollapsed());
  }

  @HostListener('window:resize', ['$event'])
  onResize(event: Event): void {
    const windowWidth = (event.target as Window).innerWidth;
    this.isSidebarCollapsedChange.emit(windowWidth < 1000);
  }

  get userRoleId() {
    return this.userService.userInfo?.roleId ?? 0;
  }
  openProfileDrawer(): void {
    this.profileDrawerVisible = true;
  }
  closeProfileDrawer(): void {
    this.profileDrawerVisible = false;
  }
  visibleProfileAfterDelay(duration: number): void {
    setTimeout(() => {
      this.profileDrawerVisible = true;
    }, duration);
  }
}
