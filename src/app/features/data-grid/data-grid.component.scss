.cart-btn {
  background: none;
  border: none;
  color: #1890ff;
  cursor: pointer;
}

.no-border {
  background: none;
  border: none;
}

.order-btn {
  background-color: #1890ff;
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;

  &:hover {
    background-color: #40a9ff;
  }
}

.action-container {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.icon-position {
  position: absolute;
  padding-top: 5px;
}

.blue-info-icon {
  color: var(--primary-button-color);
}

.left-aligned-pagination ::ng-deep .ant-table-pagination.ant-pagination {
  display: flex;
  justify-content: flex-start;
}

.ant-skeleton-input {
  border-radius: 12px !important;
}

.sort-icons {
  display: flex;
  flex-direction: column;
  float: right;
  align-items: center;
  justify-content: center;
  cursor: pointer;

  i {
    color: #fff;
    margin: 0;
    padding: 0;
  }
}

tr.darker-Row:nth-child(even) td {
  background-color: var(--table-even-row-bg-color, #f2f2f2);
}

tr:nth-child(odd) td {
  background-color: var(--table-odd-row-bg-color, #ffffff);
}

tr {
  transition: all 0.2s ease-in-out;

  th,
  td {
    font-family: 'Roboto', sans-serif;
    height: 56px !important;
    transition: all 0.2s ease-in-out;
  }

  th {
    padding-left: 18px !important;
    font-weight: 550;
    font-size: 14px;

    // White separator line
    &:not(:last-child)::after {
      content: '';
      position: absolute;
      top: 30%;
      bottom: 30%;
      right: 0;
      width: 1px;
      background-color: white;
    }
  }

  &:last-child {
    td {
      border-bottom: 1px solid var(--table-border-color, #afafaf);
    }
  }

  td {
    &:first-child {
      border-left: 1px solid var(--table-border-color, #afafaf);
    }

    &:last-child {
      border-right: 1px solid var(--table-border-color, #afafaf);

      // &::after {
      //   content: '';
      //   position: absolute;
      //   top: 0;
      //   bottom: 0;
      //   right: 0;
      //   width: 15px;
      //   background-color: var(--table-even-row-bg-color, #f2f2f2);
      //   ;
      // }
    }
  }
}
tr.stripped td:last-child::after {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  width: 15px;
  background-color: var(--table-even-row-bg-color, #f2f2f2);
}
tr.highlightRow:hover td {
  background-color: var(--table-hover-row-bg-color, #d4f2ff) !important;
}

.table-header-row th {
  border-top: 1px solid var(--table-border-color, #afafaf);
  border-bottom: 1px solid var(--table-border-color, #afafaf);
  line-height: 28px;

  &:first-child {
    border-left: 1px solid var(--table-border-color, #afafaf);
    border-top-left-radius: var(--table-edge-border-radius, 8px);
  }

  &:last-child {
    border-right: 1px solid var(--table-border-color, #afafaf);
    border-top-right-radius: var(--table-edge-border-radius, 8px);
  }
}

.table-body-row:last-child td {
  &:first-child {
    border-bottom-left-radius: var(--table-edge-border-radius, 8px);
  }

  &:last-child {
    border-bottom-right-radius: var(--table-edge-border-radius, 8px);
  }
}

tr.row-border {
  td {
    border-bottom: 2px solid rgb(198, 239, 255) !important;
  }
}
/* Style the horizontal scrollbar only */
:host ::ng-deep .ant-table-body {
  max-height: 60vh !important;

  /* WebKit browsers (Chrome, Edge, Safari) */
  &::-webkit-scrollbar {
    height: 5px;
    /* Thickness of the horizontal scrollbar */
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.5);
    /* Thumb color */
    border-radius: 5px;
  }

  &::-webkit-scrollbar-track {
    background-color: rgba(240, 240, 240, 1);
    /* Track color */
    border-radius: 5px;
  }

  /* Firefox */
  scrollbar-width: thin;
  /* Affects both scrollbars; use 'thin' for a slim scrollbar */
  scrollbar-color: rgba(0, 0, 0, 0.5) rgba(240, 240, 240, 1);
  /* Thumb and track colors */
}

/* Highlight active sorters and filters */
::ng-deep .ant-table-column-sorter-up.active,
::ng-deep .ant-table-column-sorter-down.active,
::ng-deep .ant-table-filter-trigger.active,
.active {
  color: rgb(24, 144, 255) !important;
}

/* Pagination - common styles */
::ng-deep .ant-pagination.ant-pagination-mini {
  .ant-pagination-total-text {
    padding: 0.85rem 0.75rem;
    line-height: 0;
    background-color: #fff;
    border: 1px solid #ddd;
  }

  .ant-pagination-item,
  .ant-pagination-next,
  .ant-pagination-prev {
    line-height: 28px;
    height: 28px;
    min-width: 28px;
    border-radius: 2px;
    border: 1px solid #ddd;
    margin: 0 4px;
  }

  .ant-pagination-item:not(.ant-pagination-item-active),
  .ant-pagination-prev,
  .ant-pagination-next {
    border-color: #ddd !important;
  }
}

/* Select component adjustment */
::ng-deep
  .ant-select-single.ant-select-sm:not(.ant-select-customize-input)
  .ant-select-selector {
  height: 28px;
  margin: 0 4px;
}

::ng-deep .cdk-drag-preview {
  display: table;
}

.cdk-drag-preview {
  border: none;
  border-radius: 4px;
  box-shadow:
    0 5px 5px -3px rgba(0, 0, 0, 0.2),
    0 8px 10px 1px rgba(0, 0, 0, 0.14),
    0 3px 14px 2px rgba(0, 0, 0, 0.12);
}

.cdk-drag-placeholder {
  opacity: 0;
}

.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.textHead-style {
  font-weight: bold;
  display: block;
}

.fontstyle {
  color: #6c757d;
}

::ng-deep .ant-table-thead th.ant-table-column-sort {
  background: #daecff;
}

.icons {
  font-size: 28px;
}

.primary-icons {
  color: var(--primary-button-color);
}

.badge-icon {
  position: absolute;
  top: -1rem;
  right: -1rem;
}

.profile-image {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  object-fit: cover;
}

.fullname {
  font-size: 14px;
  color: #333;
}

nz-button {
  border-radius: 20px;
  padding: 8px 16px;
}

.action-container {
  display: flex;
  justify-content: center;
  gap: 8px;
}


.input-with-icon {
  width: 100%;
}

.input-with-icon-field {
  padding-right: 30px; 
}

.save-icon {
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  color: #195fac; 
  font-size: 20px;
  margin-top: 20px;
}

.save-icon.disabled {
  cursor: not-allowed;
  color: #d9d9d9; 
}
