import {
  ChangeDetectorRef,
  Component,
  EventEmitter,
  inject,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { NzTableModule, NzTableQueryParams } from 'ng-zorro-antd/table';
import { NzButtonModule } from 'ng-zorro-antd/button';
import {
  DEFAULT_PAGE_OFFSET_OPTIONS,
  DEFAULT_PAGE_SIZE,
} from '../../core/constants/common';
import {
  IFieldDataChangeOutput,
  IRowClickOutput,
  ITableColumn,
  ITableDataChangeOutput,
  ITableDataClickOutput,
} from '../../core/interface/table';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { FormsModule } from '@angular/forms';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzSwitchModule } from 'ng-zorro-antd/switch';
import {
  CdkDrag,
  CdkDragDrop,
  CdkDropList,
  moveItemInArray,
} from '@angular/cdk/drag-drop';
import { cloneDeep } from 'lodash';
import { IDropdownOption } from '../../core/interface/common';
import { EnumSortType } from '../../core/enumerations/sort-type';
import { DataGridService } from '../../core/services/data-grid.service';
import { NzSkeletonModule } from 'ng-zorro-antd/skeleton';
import { formatDateField } from '../../utils';

@Component({
  selector: 'app-data-grid',
  standalone: true,
  imports: [
    CommonModule,
    NzTableModule,
    NzButtonModule,
    NzDropDownModule,
    FormsModule,
    NzIconModule,
    NzCheckboxModule,
    CdkDropList,
    NzToolTipModule,
    NzSelectModule,
    CdkDrag,
    NzSkeletonModule,
    NzSwitchModule,
  ],
  templateUrl: './data-grid.component.html',
  styleUrls: ['./data-grid.component.scss'],
})
export class DataGridComponent<T> implements OnInit, OnChanges {
  @Input() tableData: T[] = [];
  @Input() tableColumns: ITableColumn<T>[] = [];

  @Input() trackById = 'id';
  @Input() loading = true;
  @Input() total = 0;
  @Input() showPagination = true;
  @Input() backendPagination = false;
  @Input() frontPagination = true;
  @Input() backendSort = false;
  @Input() showTotal = true;
  @Input() showSizeChanger = true;
  @Input() showQuickJumper = true;
  @Input() borderedGrid = false;
  @Input() color = 'var(--table-header-text-color, #ffffff)';
  @Input() headerBgColor = 'var(--table-header-bg-color, #195fac)';
  @Input() headerTextAlign: 'center' | 'start' | 'end' = 'start';
  @Input() headerBorder = false;
  @Input() evenRowdarker = true;
  @Input() stripAtLastColumn = true;
  @Input() roundedCorners = true;
  @Input() rowBorder = false;

  @Input() defaultSortOrder: EnumSortType | null = null;
  @Input() defaultSortParam: string | null = null;

  @Input() dragEnabled = false;
  @Input() pageIndex = 1;
  @Input() pageSize = DEFAULT_PAGE_SIZE;
  @Input() pageSizeOptions = DEFAULT_PAGE_OFFSET_OPTIONS;

  @Input() scrollX: string | null = null;
  @Input() scrollY: string | null = '50vh';

  @Input() rowPointer = false;

  @Input() paginationPosition: 'top' | 'both' | 'bottom' = 'bottom';
  @Input() paginationHorizontalPosition: 'left' | 'right' = 'left';
  @Input() searchText = '';

  @Output() pageIndexChange = new EventEmitter<number>();
  @Output() pageSizeChange = new EventEmitter<number>();
  @Output() tableDataClick = new EventEmitter<ITableDataClickOutput<T>>();
  @Output() rowClick = new EventEmitter<IRowClickOutput<T>>();
  @Output() sortChange = new EventEmitter<{
    sortParam: string;
    sortOrder: EnumSortType | null;
  }>();
  @Output() resetSortOrder = new EventEmitter<void>();
  @Output() querryParamChange = new EventEmitter<NzTableQueryParams>();
  @Output() fieldDataChange = new EventEmitter<IFieldDataChangeOutput<T>>();
  @Output() checkboxChangeHandler = new EventEmitter<
    IFieldDataChangeOutput<T>
  >();
  @Output() tableDataChange = new EventEmitter<ITableDataChangeOutput<T>>();
  @Output() querryParamsChange = new EventEmitter<NzTableQueryParams>();

  private dataGridService = inject(DataGridService);
  private cdr = inject(ChangeDetectorRef);

  filteredData: T[] = [];
  updatedTableColumn: ITableColumn<T>[] = [];

  enumSortType = EnumSortType;

  sortOrder: EnumSortType | null = null;
  sortParam: string | null = null;
  searchValue = '';
  columnFreezed = false;

  formatDateField = formatDateField;

  ngOnInit() {
    this.filteredData = cloneDeep(this.tableData);
    this.updatedTableColumn = cloneDeep(this.tableColumns);
  }

  ngOnChanges(changes: SimpleChanges): void {
    this.handleTableHeaderHiddenStyles();
    if ('tableData' in changes || 'loading' in changes) {
      this.filteredData = cloneDeep(this.tableData);
      this._updateDisplayData();
    }
  }

  handleTableHeaderHiddenStyles() {
    const el = document.querySelector(
      '.nz-table-hide-scrollbar',
    ) as HTMLElement;

    if (el) {
      el.style.overflow = 'hidden';
    }
  }

  private _updateDisplayData() {
    const sortColumn =
      this.sortParam || this.defaultSortParam
        ? this.dataGridService.getColumnByField(
            this.updatedTableColumn,
            this.sortParam || this.defaultSortParam,
          )
        : null;
    const sortOrder = this.sortOrder || this.defaultSortOrder;
    if (sortColumn) {
      if (!this.backendSort) {
        this.dataGridService.resetSortOrder(this.updatedTableColumn);
        this.sortData(sortColumn, sortOrder);
      } else {
        sortColumn.sortOrder = sortOrder;
      }
    }
  }

  onRowClick(event: Event, rowData: T, rowIndex: number, rowNumber: number) {
    event.stopPropagation();
    this.rowClick.emit({ rowData, rowIndex, rowNumber });
  }

  getRowClassName(index: number) {
    return `row-${index}`;
  }

  // Generate empty rows if no data
  get displayRows() {
    return !this.loading ? [...this.filteredData] : Array(10).fill({} as T);
  }

  get nzScroll(): { x: string | null; y: string | null } {
    return {
      x: window.innerWidth < 880 ? '50rem' : this.scrollX,
      y: this.scrollY,
    };
  }

  getRowNumber(rowIndex: number): number {
    return (this.pageIndex - 1) * this.pageSize + rowIndex + 1;
  }

  getClassName(
    rowData: T,
    actionField: ITableColumn<T>,
    isChild = false,
  ): string {
    return actionField.getClassName?.(rowData, isChild) ?? '';
  }

  highlight(text: string | number | null): string | null {
    if (!text) {
      return null;
    } else if (!this.searchText) {
      return text + '';
    }
    text = text + '';
    const re = new RegExp(this.searchText, 'gi');
    if (re.test(text)) {
      return text.replace(re, (match) => `<mark>${match}</mark>`);
    }
    return text;
  }

  isBtnDisabled<T>(rowData: T, actionField: ITableColumn<T>): boolean {
    return !!actionField.disableFn?.(rowData);
  }

  onRowDataClick(
    event: Event,
    rowData: T,
    actionField: string,
    rowIndex: number,
    rowNumber?: number,
    link?: string,
  ) {
    event.stopPropagation();
    this.tableDataClick.emit({
      rowData,
      actionField,
      rowIndex,
      rowNumber,
      link,
    });
  }

  onFieldDataChange(rowData: T, column: ITableColumn<T>, event: Event) {
    this.fieldDataChange.emit({ rowData, column, event });
  }

  onCheckboxChange(rowData: T, event: Event) {
    event.stopPropagation();
    this.checkboxChangeHandler.emit({ rowData, event });
  }

  drop(event: CdkDragDrop<string[]>): void {
    if (this.dragEnabled) {
      const updatedData = cloneDeep(this.tableData);
      moveItemInArray(updatedData, event.previousIndex, event.currentIndex);
      const dataChangeOutPut: ITableDataChangeOutput<T> = {
        updatedData: updatedData,
        changeType: 'drag&drop',
      };
      this.tableDataChange.emit(dataChangeOutPut);
    }
  }

  getValueById(
    value: number | number[],
    dropDownItems?: IDropdownOption<T>[],
    bindValue?: 'id' | string,
    bindLabel?: 'name' | string,
    fieldName?: string,
  ): string {
    if (
      value !== null &&
      value !== undefined &&
      dropDownItems &&
      bindLabel &&
      bindValue
    ) {
      // For multivalued dropdown
      if (Array.isArray(value)) {
        const items = value.map((val: number): string =>
          this.getValueById(
            val,
            dropDownItems,
            bindValue,
            bindLabel,
            fieldName,
          ),
        );
        return items.join(', ');
      } else {
        const selectedItem = dropDownItems.find(
          (item) => item[bindValue as 'id'] === value,
        );
        return selectedItem?.[bindLabel as 'name'] ?? '';
      }
    } else {
      return value ? value.toString() : '';
    }
  }

  isMultiValuedDropdown(value: number[] | string[] | string): boolean {
    return Array.isArray(value);
  }

  sortData(column: ITableColumn<T>, sortOrder: EnumSortType | null) {
    if (column.sortOrder !== sortOrder) {
      this.dataGridService.resetSortOrder<T>(this.updatedTableColumn);
      column.sortOrder = this.sortOrder = sortOrder;
      this.sortParam = column.field;
      const dataChangeOutPutForSort: ITableDataChangeOutput<T> = {
        updatedData: this.filteredData,
        changeType: 'sort',
        changeValue: { SortOrder: this.sortOrder, SortParam: this.sortParam },
        column,
      };
      this.sortChange.emit({ sortOrder, sortParam: this.sortParam });
      this.tableDataChange.emit(dataChangeOutPutForSort);
    }
  }

  onResetSortOrder() {
    this.dataGridService.resetSortOrder(this.updatedTableColumn);
    this.sortOrder = this.defaultSortOrder ?? null;
    this.sortParam = this.defaultSortParam ?? null;
    this._updateDisplayData();

    const dataChangeOutPut: ITableDataChangeOutput<T> = {
      updatedData: this.filteredData,
      changeType: 'sort',
      changeValue: { SortOrder: this.sortOrder, SortParam: this.sortParam },
    };
    this.tableDataChange.emit(dataChangeOutPut);
    this.resetSortOrder.emit();
  }

  onPageSizeChange(pageSize: number) {
    this.pageSizeChange.emit(pageSize);
  }

  onPageIndexChange(pageIndex: number) {
    this.pageIndexChange.emit(pageIndex);
  }

  onQuerryParamsChange(querryParams: NzTableQueryParams) {
    this.querryParamChange.emit(querryParams);
  }

  onFilterChange(event: keyof T, column: ITableColumn<T>) {
    const dataChangeOutPutForFilter: ITableDataChangeOutput<T> = {
      updatedData: this.filteredData,
      changeType: 'filter',
      changeValue: event,
      column,
    };
    this.tableDataChange.emit(dataChangeOutPutForFilter);
  }

  onImageError(event: Event) {
    (event.target as HTMLImageElement).src = 'assets/profileImage.png';
  }
}
