<!-- HTML Template -->
<div style="height: 100vh" class="fixed-splitter">
  <div>
    <div class="right-panel">
      <!-- Cart View -->
      <div *ngIf="currentState === PaymentState.CART && !showRechargeView">
        
        <!-- Wallet Balance Section -->
        <div class="wallet-section">
          <p class="balance-label">Current Wallet Balance</p>
          <div class="balance-row">
            <h2 class="wallet-balance">${{ walletBalance.toFixed(2) }}</h2>
            <button
            nz-button 
            nzType="primary"
              data-testid="recharge-wallet"
              (click)="rechargeWallet()"
            >
              Recharge
            </button>
          </div>
        </div>

        <!-- Payment Summary Section -->
        <div class="payment-summary-section">
          <h4 class="section-title">Payment Summary</h4>
          
          <!-- Order Items List -->
          <div class="order-items-list">
            <div class="order-item-row" *ngFor="let order of orders; let i = index">
              <span class="item-name">{{ order.productName }}:</span>
              <div class="item-actions">
                <span class="item-price">${{ order.price?.toFixed(2) }}</span>
                <button
                  class="delete-btn"
                  data-testid="delete-cart-item"
                  (click)="removeItem(order.id ?? 0)"
                  title="Remove item"
                >
                  <i nz-icon nzType="delete" nzTheme="outline"></i>
                </button>
              </div>
            </div>
          </div>

          <!-- Total Section -->
          <div class="total-section">
            <div class="total-row">
              <span class="total-label">Total:</span>
              <span class="total-amount">${{ finalPrice.toFixed(2) || '0.00' }}</span>
            </div>
          </div>
        </div>

        <!-- Pay Button -->
        <button
          class="pay-btn"
          data-testid="pay-btn"
          (click)="createOrder()"
        >
          Pay ${{ finalPrice.toFixed(2) || '0.00' }}
        </button>
      </div>

      <!-- Recharge View -->
      <div *ngIf="showRechargeView">
        <!-- Breadcrumb stays here -->
        <div class="drawer-breadcrumb">
          <span class="breadcrumb-item" (click)="showRechargeView = false">Cart</span>
          <span class="breadcrumb-separator">/</span>
          <span class="breadcrumb-item active">Recharge</span>
        </div>

        <!-- Wallet Component handles Recharge UI -->
        <app-wallet
          [(amount)]="amount"
          [visibleWallet]="visible"
          (visibleWalletChange)="visibleChange.emit($event)"
          (visibleWalletAfterDelay)="visibleAfterDelay.emit($event)"
        ></app-wallet>
      </div>
    </div>
  </div>
</div>
