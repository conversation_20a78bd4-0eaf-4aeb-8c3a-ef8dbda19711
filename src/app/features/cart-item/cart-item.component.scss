.fixed-splitter {
  display: flex;
  flex-direction: column;
}

.right-panel {
  background-color: #eef7ff;
  min-height: 100%;
  padding: 24px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.wallet-section {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 10rem;
}

.balance-label {
  font-size: 14px;
  color: #333;
  margin: 0 0 8px 0;
  font-weight: 500;
}

.balance-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.wallet-balance {
  font-size: 28px;
  font-weight: bold;
  color: #333;
  margin: 0;
}

.recharge-btn {
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.3s ease;

  &:hover {
    background: #40a9ff;
  }
}

.payment-summary-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.order-items-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.order-item-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.item-name {
  font-size: 14px;
  color: #333;
  flex: 1;
}

.item-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.item-price {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.delete-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  font-size: 16px;
  color: #1890ff;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    color: #40a9ff;
    transform: scale(1.1);
  }
}

.total-section {
  background-color: #f5f5f5;
  padding: 12px 0;
  margin-top: 16px;
  border-radius: 4px;
}

.total-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.total-label {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.total-amount {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.pay-btn {
  width: 100%;
  background: #000000;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 16px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
  margin-top: auto;

  &:hover {
    background: #333333;
  }
}

.drawer-breadcrumb {
  margin-bottom: 16px;
}

.breadcrumb-item {
  font-size: 14px;
  color: #666;
  cursor: pointer;
  transition: color 0.3s ease;

  &:hover {
    color: #1890ff;
  }

  &.active {
    color: #333;
    cursor: default;
  }
}

.breadcrumb-separator {
  margin: 0 8px;
  color: #999;
}

@media (max-width: 768px) {
  .right-panel {
    padding: 15px;
  }

  .balance-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .order-item-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;

    .item-actions {
      width: 100%;
      justify-content: space-between;
    }
  }
}
