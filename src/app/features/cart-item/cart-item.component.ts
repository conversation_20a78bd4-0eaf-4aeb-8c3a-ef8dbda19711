import {
  Component,
  OnInit,
  OnDestroy,
  Output,
  EventEmitter,
  Input,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router, NavigationEnd, ActivatedRoute } from '@angular/router';
import { Subscription } from 'rxjs';
import { filter } from 'rxjs/operators';

// NG-ZORRO imports
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzListModule } from 'ng-zorro-antd/list';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzTypographyModule } from 'ng-zorro-antd/typography';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzResultModule } from 'ng-zorro-antd/result';
import { NzBreadCrumbModule } from 'ng-zorro-antd/breadcrumb';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzInputNumberModule } from 'ng-zorro-antd/input-number';

// Services
import { CartService } from '../../core/services/cart.service';
import { NotificationService } from '../../core/services/notification.service';
import { SharedService } from '../../core/services/shared.service';
import { RechargeService } from '../../core/services/recharge.service';
import { UserService } from '../../core/services/user.service';
import { TransactionService } from '../../core/services/transaction.service';

// Constants and DTOs
import {
  COMMON_STRINGS,
  DEFAULT_CURRENCY,
  DEFAULT_RECHARGE_AMOUNT,
} from '../../core/constants/common';
import { ROUTES } from '../../core/constants/routes';
import { CartItemDTO, TransactionDTO, UserResponseDTO } from '../../api-client';

// Enums
import { PaymentState } from '../../core/enumerations/payment-state';
import { EnumTransactionTypes } from '../../core/enumerations/transaction-types';

// Components
import { WalletComponent } from '../../core/features/wallet/wallet.component';

@Component({
  selector: 'app-cart-item',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    NzCardModule,
    NzIconModule,
    NzListModule,
    NzButtonModule,
    NzDividerModule,
    NzTypographyModule,
    NzSpinModule,
    NzResultModule,
    NzBreadCrumbModule,
    NzInputModule,
    NzInputNumberModule,
    WalletComponent,
  ],
  templateUrl: './cart-item.component.html',
  styleUrls: ['./cart-item.component.scss'],
})
export class CartItemComponent implements OnInit, OnDestroy {
  @Input() visible = false;
  @Output() visibleChange = new EventEmitter<boolean>();
  @Output() visibleAfterDelay = new EventEmitter<number>();

  // Constants
  readonly PaymentState = PaymentState;
  readonly currency = DEFAULT_CURRENCY;

  // Component state
  walletBalance = 0;
  orders: CartItemDTO[] = [];
  finalPrice = 0;
  currentState: PaymentState = PaymentState.CART;
  paymentAmount = 0;
  transactionId = '';
  showRechargeView = false;
  amount = DEFAULT_RECHARGE_AMOUNT;

  // Data arrays
  rechargeTransactions: TransactionDTO[] = [];

  // Private properties
  private routerSubscription?: Subscription;
  private userId?: number;

  constructor(
    private cartService: CartService,
    private notificationService: NotificationService,
    private sharedService: SharedService,
    private rechargeService: RechargeService,
    private userService: UserService,
    private router: Router,
    private route: ActivatedRoute,
    private transactionService: TransactionService,
  ) {}

  async ngOnInit(): Promise<void> {
    await this.initializeUser();
    this.loadCartData();
    this.subscribeToRouterEvents();
  }

  ngOnDestroy(): void {
    this.routerSubscription?.unsubscribe();
  }

  // Getters
  get userName(): string {
    return this.userService.userInfo?.firstName ?? 'John Doe';
  }

  // Cart management methods
  loadCartItems(): void {
    this.cartService.getCart().subscribe({
      next: (response) => {
        this.orders = response.items ?? [];
        this.sharedService.cartItems = this.orders;
        this.calculateFinalPrice();
      },
      error: () => {
        this.notificationService.error(
          COMMON_STRINGS.errorMessages.failedToFetchCartItems,
        );
      },
    });
  }

  removeItem(cartItemId: number): void {
    this.cartService.removeFromCart(cartItemId).subscribe({
      next: () => {
        this.loadCartItems();
        this.notificationService.successMessage(
          COMMON_STRINGS.successMessages.removeFromCartSuccess,
        );
      },
      error: (err) => {
        this.notificationService.error(
          `Failed to remove item: ${err.message || 'Unknown error'}`,
        );
      },
    });
  }

  // Order and payment methods
  createOrder(): void {
    if (this.isCartEmpty()) {
      this.notificationService.warning(
        COMMON_STRINGS.warningMessages.cartEmptyMessage,
      );
      return;
    }

    if (this.finalPrice > this.walletBalance) {
      this.notificationService.errorMessage(
        COMMON_STRINGS.errorMessages.insufficientWalletBalance,
      );
      return;
    }

    this.paymentAmount = this.finalPrice;
    this.currentState = PaymentState.PROCESSING;

    const orderItemIds = this.orders.map((order) => order.id ?? 0);
    this.processPayment(orderItemIds);
  }

  proceedToTransaction(): void {
    const transactionIdNumber = parseInt(this.transactionId) || 0;
    this.redirectToTransaction(transactionIdNumber);
  }

  backToCart(): void {
    this.currentState = PaymentState.CART;
  }

  // Wallet methods
  rechargeWallet(): void {
    this.showRechargeView = true;
  }

  onRechargeViewTimeout(delay: number): void {
    setTimeout(() => {
      this.showRechargeView = false;
    }, delay);
  }

  // User methods
  getCurrentUser(): UserResponseDTO {
    const userInfo = this.userService.userInfo;
    return {
      firstName: userInfo?.firstName,
      roleName: userInfo?.roleName ?? 'N/A',
      contactNumber: userInfo?.contactNumber ?? 'N/A',
      roleId: userInfo?.roleId,
      primaryAddress: userInfo?.primaryAddress,
      billingAddress: userInfo?.billingAddress,
      email: userInfo?.email ?? 'N/A',
    };
  }

  // Private helper methods
  private async initializeUser(): Promise<void> {
    try {
      const userInfo = await this.userService.getUserRole();
      if (userInfo?.id) {
        this.userId = userInfo.id;
      } else {
        this.handleError(
          'User information not available. Please log in again.',
        );
      }
    } catch {
      this.handleError(COMMON_STRINGS.errorMessages.handleUserError);
    }
  }

  private loadCartData(): void {
    this.loadCartItems();
    this.loadWalletBalance();
  }

  private subscribeToRouterEvents(): void {
    this.routerSubscription = this.router.events
      .pipe(filter((event) => event instanceof NavigationEnd))
      .subscribe((event: NavigationEnd) => {
        if (event.url.includes('/cart-item')) {
          this.loadCartData();
        }
      });
  }

  private loadWalletBalance(): void {
    if (!this.isUserAuthenticated()) return;

    this.rechargeService.getUserWalletBalance(this.userId!).subscribe({
      next: (balance) => {
        this.walletBalance = balance;
      },
      error: () => {
        this.notificationService.error(
          COMMON_STRINGS.errorMessages.failedToFetchWalletBalance,
        );
      },
    });
  }

  private loadRechargeTransactions(): void {
    this.transactionService.getAllTransactions('USER').subscribe({
      next: (transactions) => {
        this.rechargeTransactions = transactions.filter(
          (txn) => txn.transactionType === EnumTransactionTypes.RECHARGE,
        );
      },
    });
  }

  private processPayment(orderItemIds: number[]): void {
    this.cartService.createOrder(orderItemIds).subscribe({
      next: (response) => {
        this.transactionId =
          response.transaction?.transactionId?.toString() ?? 'N/A';
        this.currentState = PaymentState.SUCCESS;
        this.loadCartItems();
        this.loadWalletBalance();

        this.router.navigate([ROUTES.mockPayment], {
          queryParams: { paymentAmount: this.finalPrice },
        });

        this.visibleChange.emit(false);
        setTimeout(() => {
          this.redirectToTransaction(response.transaction?.transactionId ?? 0);
        }, 6000);
      },
      error: () => {
        this.currentState = PaymentState.CART;
        this.notificationService.error(
          COMMON_STRINGS.errorMessages.failedToCreateOrder,
        );
      },
    });
  }

  private redirectToTransaction(transactionId: number): void {
    this.visibleChange.emit(!this.visible);
    this.router.navigate([
      ROUTES.sidebar.purchases,
      ROUTES.sidebar.transactions,
      transactionId,
    ]);
  }

  private calculateFinalPrice(): void {
    this.finalPrice = this.orders.reduce(
      (sum, order) => sum + (order?.finalPrice ?? 0),
      0,
    );
  }

  private isCartEmpty(): boolean {
    return this.orders.length === 0;
  }

  private isUserAuthenticated(): boolean {
    if (!this.userId) {
      this.handleError(COMMON_STRINGS.errorMessages.handleUserError);
      return false;
    }
    return true;
  }

  private handleError(message: string): void {
    this.notificationService.error(message);
  }
}
