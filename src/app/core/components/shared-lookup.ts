import { Injectable } from '@angular/core';
import { Observable, of, BehaviorSubject } from 'rxjs';
import { map, catchError, shareReplay } from 'rxjs/operators';
import {
  ApiResponseListStateDTO,
  ApiResponseListZipCodeDTO,
  ApiResponseObject,
  StateDTO,
  ZipCodeDTO,
  RoleDTO,
  LookUpControllerService,
} from '../../api-client';
import { COMMON_STRINGS, EMAIL_REGEX } from '../constants/common';
import { NotificationService } from '../services/notification.service';
import { RoleService } from '../services/role.service';

@Injectable({
  providedIn: 'root',
})
export class SharedLookup {
  // Cache for states to avoid repeated API calls
  private statesCache$: Observable<StateDTO[]> | null = null;
  private rolesCache$: Observable<RoleDTO[]> | null = null;

  // Loading states
  private loadingSubject = new BehaviorSubject<boolean>(false);
  public loading$ = this.loadingSubject.asObservable();

  constructor(
    private lookUpControllerService: LookUpControllerService,
    private roleService: RoleService,
    private notification: NotificationService,
  ) {}

  /**
   * Fetch all states with caching
   */
  fetchStates(): Observable<StateDTO[]> {
    if (this.statesCache$) {
      return this.statesCache$;
    }

    this.loadingSubject.next(true);
    this.statesCache$ = this.lookUpControllerService.getStates().pipe(
      map((response: ApiResponseListStateDTO) => {
        this.loadingSubject.next(false);
        const states = response.data || [];
        if (states.length === 0) {
          this.notification.warning(
            COMMON_STRINGS.errorMessages.failedToFetchStates,
          );
        }
        return states;
      }),
      catchError((error) => {
        this.loadingSubject.next(false);
        this.notification.error(
          COMMON_STRINGS.errorMessages.failedToFetchStates,
        );
        console.error('Failed to fetch states:', error);
        return of([]);
      }),
      shareReplay(1), // Cache the result
    );

    return this.statesCache$;
  }

  /**
   * Fetch zipcodes for a specific state
   */
  fetchZipcodes(stateId: number): Observable<ZipCodeDTO[]> {
    if (!stateId) {
      return of([]);
    }

    this.loadingSubject.next(true);
    return this.lookUpControllerService.getZipCodes(stateId).pipe(
      map((response: ApiResponseListZipCodeDTO) => {
        this.loadingSubject.next(false);
        const zipcodes = response.data || [];
        if (zipcodes.length === 0) {
          this.notification.warning(
            COMMON_STRINGS.errorMessages.failedToFetchZipcodes,
          );
        }
        return zipcodes;
      }),
      catchError((error) => {
        this.loadingSubject.next(false);
        this.notification.error(
          COMMON_STRINGS.errorMessages.failedToFetchZipcodes,
        );
        console.error('Failed to fetch zipcodes:', error);
        return of([]);
      }),
    );
  }

  /**
   * Fetch roles with caching and filtering
   */
  fetchRoles(): Observable<RoleDTO[]> {
    if (this.rolesCache$) {
      return this.rolesCache$;
    }

    this.loadingSubject.next(true);
    this.rolesCache$ = this.roleService.getAllRoles().pipe(
      map((response: ApiResponseObject) => {
        this.loadingSubject.next(false);
        const roles = ((response.data as { content?: RoleDTO[] })?.content ||
          []) as RoleDTO[];

        // Filter only Admin and Member roles
        const filteredRoles = roles.filter(
          (role) =>
            role.displayText === 'Admin' || role.displayText === 'Member',
        );

        if (filteredRoles.length < 2) {
          this.notification.error(
            COMMON_STRINGS.errorMessages.displayRolesFailed,
          );
        }

        return filteredRoles;
      }),
      catchError((error) => {
        this.loadingSubject.next(false);
        this.notification.error(
          COMMON_STRINGS.errorMessages.displayRolesFailed,
        );
        console.error('Failed to fetch roles:', error);
        return of([]);
      }),
      shareReplay(1), // Cache the result
    );

    return this.rolesCache$;
  }

  /**
   * Validate email format using EMAIL_REGEX constant
   */
  isValidEmail(email: string): boolean {
    return EMAIL_REGEX.test(email);
  }

  /**
   * Clear all caches
   */
  clearCache(): void {
    this.statesCache$ = null;
    this.rolesCache$ = null;
  }
}
