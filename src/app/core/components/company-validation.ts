import { Injectable } from '@angular/core';
import { FormGroup, Validators, ValidatorFn } from '@angular/forms';
import { COMMON_STRINGS, PHONE_REGEX } from '../constants/common';
import { ICompanyFields } from '../interface/company-fields';
import { AddressResponseDTO } from '../../api-client';

@Injectable({
  providedIn: 'root',
})
export class CompanyValidation {
  /**
   * Get error tip for form control
   */
  getCompanyErrorTip(form: FormGroup, controlName: string): string | undefined {
    const control = form.get(controlName);
    if (!control?.touched) return undefined;

    if (control.hasError('required')) {
      return `${controlName.charAt(0).toUpperCase() + controlName.slice(1)} is required`;
    }

    const errorMap: Record<string, string> = {
      billingEmail: COMMON_STRINGS.warningMessages.enterValidEmail,
      accountsContactNumber:
        COMMON_STRINGS.warningMessages.enterValidPhoneNumber,
    };

    if (
      controlName in errorMap &&
      control.hasError(controlName === 'billingEmail' ? 'email' : 'pattern')
    ) {
      return errorMap[controlName];
    }

    return undefined;
  }

  /**
   * Check if company data is unchanged
   */
  isCompanyDataUnchanged(
    original: ICompanyFields,
    updated: ICompanyFields,
  ): boolean {
    return (
      this.compareBasicFields(original, updated) &&
      this.compareAddressFields(original, updated)
    );
  }

  /**
   * Handle invalid form
   */
  handleInvalidForm(form: FormGroup): void {
    form.markAllAsTouched();
  }

  /**
   * Update billing form validators based on sameAsCompanyDetails flag
   */
  updateBillingFormValidators(
    form: FormGroup,
    sameAsCompanyDetails: boolean,
  ): void {
    const fields = [
      'name',
      'abn',
      'acn',
      'billingEmail',
      'accountsContactName',
      'accountsContactNumber',
      'addressLine1',
      'addressLine2',
      'suburb',
      'state',
      'postalCode',
    ];

    if (sameAsCompanyDetails) {
      this.setSameAsCompanyValidators(form, fields);
    } else {
      this.setIndependentBillingValidators(form, fields);
    }

    this.updateFieldsValidation(form, fields);
  }

  /**
   * Set validators for when billing is same as company details
   */
  private setSameAsCompanyValidators(form: FormGroup, fields: string[]): void {
    fields.forEach((field) => {
      const control = form.get(field);
      if (control) {
        control.clearValidators();
        control.disable({ emitEvent: false });
      }
    });
    form.get('billingEmail')?.setValidators([Validators.email]);
  }

  /**
   * Set validators for independent billing details
   */
  private setIndependentBillingValidators(
    form: FormGroup,
    fields: string[],
  ): void {
    fields.forEach((field) => {
      const control = form.get(field);
      if (control) {
        control.enable({ emitEvent: false });
        control.setValidators(this.getValidatorsForField(field));
      }
    });

    if (!form.get('state')?.value) {
      form.get('postalCode')?.disable({ emitEvent: false });
    }
  }

  /**
   * Get validators for specific field
   */
  private getValidatorsForField(field: string): ValidatorFn[] {
    const validatorMap: Record<string, ValidatorFn[]> = {
      billingEmail: [Validators.required, Validators.email],
      accountsContactNumber: [Validators.pattern(PHONE_REGEX)],
    };

    if (['abn', 'acn', 'addressLine1', 'state', 'postalCode'].includes(field)) {
      return [Validators.required];
    }

    return validatorMap[field] || [];
  }

  /**
   * Update validation for all fields
   */
  private updateFieldsValidation(form: FormGroup, fields: string[]): void {
    fields.forEach((field) => {
      form.get(field)?.updateValueAndValidity({ emitEvent: false });
    });
  }

  /**
   * Compare basic company fields
   */
  private compareBasicFields(
    original: ICompanyFields,
    updated: ICompanyFields,
  ): boolean {
    const fieldsToCompare: (keyof ICompanyFields)[] = [
      'name',
      'abn',
      'acn',
      'billingEmail',
      'accountsContactName',
      'accountsContactNumber',
    ];
    return fieldsToCompare.every((field) => original[field] === updated[field]);
  }

  /**
   * Compare address fields
   */
  private compareAddressFields(
    original: ICompanyFields,
    updated: ICompanyFields,
  ): boolean {
    const addressFields: (keyof AddressResponseDTO)[] = [
      'addressLine1',
      'addressLine2',
      'suburb',
      'stateId',
      'zipCodeId',
    ];

    return addressFields.every(
      (field) =>
        (original.primaryAddress?.[field] ?? '') ===
          (updated.primaryAddress?.[field] ?? '') &&
        (original.billingAddress?.[field] ?? '') ===
          (updated.billingAddress?.[field] ?? ''),
    );
  }

  /**
   * Validate form and return validation result
   */
  validateForm(form: FormGroup): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!form.valid) {
      Object.keys(form.controls).forEach((key) => {
        const control = form.get(key);
        if (control && control.invalid) {
          if (control.hasError('required')) {
            errors.push(`${key} is required`);
          }
          if (control.hasError('email')) {
            errors.push(`${key} must be a valid email`);
          }
          if (control.hasError('pattern')) {
            errors.push(`${key} format is invalid`);
          }
        }
      });
    }

    return {
      isValid: form.valid,
      errors,
    };
  }

  /**
   * Check if save button should be enabled
   */
  isSaveButtonEnabled(form: FormGroup, isLoading: boolean): boolean {
    return form.valid && form.dirty && !isLoading;
  }

  /**
   * Reset form to pristine state
   */
  resetFormState(form: FormGroup): void {
    form.markAsPristine();
    form.markAsUntouched();
  }

  /**
   * Mark form as dirty
   */
  markFormAsDirty(form: FormGroup): void {
    form.markAsDirty();
  }

  /**
   * Check if specific field has error
   */
  hasFieldError(
    form: FormGroup,
    fieldName: string,
    errorType: string,
  ): boolean {
    const control = form.get(fieldName);
    return !!(control && control.touched && control.hasError(errorType));
  }

  /**
   * Get all form errors
   */
  getAllFormErrors(form: FormGroup): Record<string, Record<string, unknown>> {
    const errors: Record<string, Record<string, unknown>> = {};

    Object.keys(form.controls).forEach((key) => {
      const control = form.get(key);
      if (control && control.errors) {
        errors[key] = control.errors;
      }
    });

    return errors;
  }

  /**
   * Check if form has any errors
   */
  hasFormErrors(form: FormGroup): boolean {
    return Object.keys(this.getAllFormErrors(form)).length > 0;
  }
}
