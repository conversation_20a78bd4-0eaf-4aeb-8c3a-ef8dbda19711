import { Injectable } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { ChangeDetectorRef } from '@angular/core';
import { StateDTO, ZipCodeDTO } from '../../api-client';
import { ICompanyFields } from '../interface/company-fields';
import { NotificationService } from '../services/notification.service';
import { RegisterService } from '../services/register.service';
import { CompanyForm } from './company-form';

@Injectable({
  providedIn: 'root',
})
export class CompanyFormHelperService {
  constructor(
    private notification: NotificationService,
    private registerService: RegisterService,
    private companyFormService: CompanyForm,
  ) {}

  async initializeForm(
    company: ICompanyFields | null,
    companyForm: FormGroup,
    states: StateDTO[],
    postalCode: ZipCodeDTO[],
    billingZipcode: ZipCodeDTO[],
    cdr: ChangeDetectorRef,
  ): Promise<void> {
    if (!company) return;

    await this.fetchStates(states, cdr);
    this.populateFormWithCompanyData(companyForm, company, states);
    await this.loadZipcodesForStates(companyForm, states, postalCode, billingZipcode);
    this.initializePostalCodeFields(companyForm);
    companyForm.markAsPristine();
    companyForm.markAsUntouched();
    cdr.detectChanges();
  }

  async fetchStates(states: StateDTO[], cdr: ChangeDetectorRef): Promise<void> {
    try {
      const fetchedStates = await this.companyFormService.fetchStates().toPromise();
      states.length = 0; // Clear existing states
      states.push(...(fetchedStates || []));
      if (states.length === 0) {
        this.notification.error('No states available. Please try again later.');
      }
    } catch (error) {
      console.error('Failed to fetch states:', error);
      this.notification.error('Failed to load states. Please try again.');
    } finally {
      cdr.detectChanges();
    }
  }

  private async loadZipcodesForStates(
    companyForm: FormGroup,
    states: StateDTO[],
    postalCode: ZipCodeDTO[],
    billingZipcode: ZipCodeDTO[],
  ): Promise<void> {
    const primaryStateId = companyForm.get('state')?.value;
    const billingStateId = companyForm.get('billingState')?.value;

    const promises = [
      primaryStateId
        ? this.fetchZipcodes(primaryStateId, 'primary', postalCode)
        : Promise.resolve(),
      billingStateId
        ? this.fetchZipcodes(billingStateId, 'billing', billingZipcode)
        : Promise.resolve(),
    ].filter((p) => p !== Promise.resolve());

    await Promise.all(promises);
  }

  async fetchZipcodes(
    stateId: number,
    addressType: 'primary' | 'billing',
    targetZipcodes: ZipCodeDTO[],
  ): Promise<void> {
    if (!stateId) return;

    try {
      const zipcodes = await this.companyFormService
        .fetchZipcodes(stateId)
        .toPromise();
      this.processZipcodes(zipcodes || [], addressType, targetZipcodes);
      this.handleZipcodesResult(zipcodes || []);
    } catch (error) {
      console.error(`Failed to fetch ${addressType} zipcodes:`, error);
      this.notification.error(
        `Failed to load ${addressType} zipcodes. Please try again.`,
      );
    }
  }

  private processZipcodes(
    zipcodes: ZipCodeDTO[],
    addressType: 'primary' | 'billing',
    targetZipcodes: ZipCodeDTO[],
  ): void {
    targetZipcodes.length = 0; // Clear existing zipcodes
    targetZipcodes.push(...zipcodes);
  }

  private handleZipcodesResult(zipcodes: ZipCodeDTO[]): void {
    if (zipcodes.length === 0) {
      this.notification.warning(
        'No zipcodes available for the selected state.',
      );
    }
  }

  private populateFormWithCompanyData(
    companyForm: FormGroup,
    company: ICompanyFields,
    states: StateDTO[],
  ): void {
    this.companyFormService.populateFormWithCompanyData(
      companyForm,
      company,
      states,
    );
  }

  private initializePostalCodeFields(companyForm: FormGroup): void {
    const primaryStateId = companyForm.get('state')?.value;
    const billingStateId = companyForm.get('billingState')?.value;

    this.handlePostalCodeFieldState(companyForm, 'postalCode', primaryStateId);
    this.handlePostalCodeFieldState(companyForm, 'billingPostalCode', billingStateId);
  }

  private handlePostalCodeFieldState(
    companyForm: FormGroup,
    fieldName: string,
    stateId: number | null,
  ): void {
    const postalCodeControl = companyForm.get(fieldName);
    if (postalCodeControl) {
      if (!stateId) {
        postalCodeControl.setValue('', { emitEvent: false });
        postalCodeControl.disable({ emitEvent: false });
      } else {
        postalCodeControl.enable({ emitEvent: false });
      }
    }
  }
}