import { inject, Injectable } from '@angular/core';
import { HttpErrorResponse } from '@angular/common/http';
import { BehaviorSubject, Subject } from 'rxjs';
import {
  ICompanyFields,
  ICompanyFormData,
  IBillingFormData,
  OnboardingState,
} from '../interface/company-fields';
import { IUserFormData } from '../interface/user-fields';
import { IndexedDBService } from '../services/indexdb.service';
import { RegisterService } from '../services/register.service';

@Injectable({
  providedIn: 'root',
})
export class CompanyState {
  private companyData: ICompanyFields | null = null;
  private companyDataSubject = new BehaviorSubject<ICompanyFields | null>(null);
  private tempCompanyDataSubject = new BehaviorSubject<ICompanyFields | null>(
    null,
  );
  private companyCreatedSubject = new Subject<ICompanyFields>();
  private registerService = inject(RegisterService);
  private indexedDBService = inject(IndexedDBService);
  private fileSelectedSubject = new Subject<{
    id: number;
    fileData: string | null;
  }>();

  // Onboarding state management
  private initialOnboardingState: OnboardingState = {
    companyDetails: null,
    billingDetails: null,
    userDetails: null,
    currentStep: 0,
  };
  private onboardingStateSubject = new BehaviorSubject<OnboardingState>(
    this.initialOnboardingState,
  );

  // Public observables
  fileSelected$ = this.fileSelectedSubject.asObservable();
  companyCreated$ = this.companyCreatedSubject.asObservable();
  onboardingState$ = this.onboardingStateSubject.asObservable();

  setCompanyData(data: ICompanyFields | null) {
    this.companyData = data;
    this.companyDataSubject.next(data);
  }

  getCompanyData(): ICompanyFields | null {
    return this.companyData;
  }

  clearCompanyData() {
    this.companyData = null;
    this.companyDataSubject.next(null);
  }

  notifyCompanyCreated(company: ICompanyFields): void {
    this.companyCreatedSubject.next(company); // Emit company creation event
  }

  toggleCompanyStatus(
    company: ICompanyFields,
    onSuccess: () => void,
    onError: (errorMessage: string) => void,
  ): void {
    if (!company.id) {
      onError('Missing company ID');
      return;
    }

    const newStatus = !company.isActive;

    this.registerService
      .activeInactiveCompany(company.id, newStatus)
      .subscribe({
        next: () => {
          company.isActive = newStatus;
          onSuccess();
        },
        error: (error: HttpErrorResponse) => {
          onError(error?.error?.message || 'Failed to update company status');
        },
      });
  }

  triggerFileInput(index: number): void {
    const fileInput = document.getElementById(
      `fileInput-${index}`,
    ) as HTMLInputElement;
    if (fileInput) {
      fileInput.click();
    } else {
      console.error(`File input with ID fileInput-${index} not found`);
    }
  }

  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files[0]) {
      const file = input.files[0];

      this.indexedDBService
        .saveImage(file)
        .then((id) => {
          if (!Number.isInteger(id) || id <= 0) {
            console.error('Invalid ID returned from saveImage');
            this.fileSelectedSubject.next({ id, fileData: null });
            return;
          }

          this.indexedDBService
            .getImage(id)
            .then((retrievedFile) => {
              if (retrievedFile) {
                this.fileSelectedSubject.next({
                  id,
                  fileData: retrievedFile.base64,
                });
              } else {
                console.error('File not found in IndexedDB');
              }
            })
            .catch((error) => {
              console.error('Error retrieving file from IndexedDB:', error);
            });

          this.fileSelectedSubject.next({ id, fileData: id.toString() });
        })
        .catch((error) => {
          console.error('Error saving file to IndexedDB:', error);
          this.fileSelectedSubject.next({ id: 0, fileData: null });
        });
    } else {
      console.error('No file selected');
      this.fileSelectedSubject.next({ id: 0, fileData: null });
    }
  }

  setTempCompanyData(company: ICompanyFields): void {
    this.tempCompanyDataSubject.next(company);
  }

  getTempCompanyData(): ICompanyFields | null {
    return this.tempCompanyDataSubject.value;
  }

  clearTempCompanyData(): void {
    this.tempCompanyDataSubject.next(null);
  }

  // Onboarding state management methods
  getCurrentOnboardingState(): OnboardingState {
    return this.onboardingStateSubject.value;
  }

  updateCompanyDetails(companyData: ICompanyFormData): void {
    const currentState = this.getCurrentOnboardingState();
    this.onboardingStateSubject.next({
      ...currentState,
      companyDetails: companyData,
    });
  }

  updateBillingDetails(billingData: IBillingFormData): void {
    const currentState = this.getCurrentOnboardingState();
    this.onboardingStateSubject.next({
      ...currentState,
      billingDetails: billingData,
    });
  }

  updateUserDetails(userData: IUserFormData): void {
    const currentState = this.getCurrentOnboardingState();
    this.onboardingStateSubject.next({
      ...currentState,
      userDetails: userData,
    });
  }

  updateCurrentStep(step: number): void {
    const currentState = this.getCurrentOnboardingState();
    this.onboardingStateSubject.next({
      ...currentState,
      currentStep: step,
    });
  }

  getCompanyDetails(): ICompanyFormData | null {
    return this.getCurrentOnboardingState().companyDetails;
  }

  getBillingDetails(): IBillingFormData | null {
    return this.getCurrentOnboardingState().billingDetails;
  }

  getUserDetails(): IUserFormData | null {
    return this.getCurrentOnboardingState().userDetails;
  }

  resetOnboardingState(): void {
    this.onboardingStateSubject.next(this.initialOnboardingState);
  }

  isReadyForSubmission(): boolean {
    const state = this.getCurrentOnboardingState();
    return !!(
      state.companyDetails &&
      state.billingDetails &&
      state.userDetails
    );
  }

  fileUploadEvent(fileInput: Event): void {
    const input = fileInput.target as HTMLInputElement;
    if (input.files && input.files.length) {
      const files = Array.from(input.files);
      for (const file of files) {
        const reader = new FileReader();
        reader.onload = (event: ProgressEvent<FileReader>) => {
          const base64 = event.target?.result as string;

          const img = new Image();
          img.onload = () => {
            this.indexedDBService
              .saveImage(file)
              .then((id) => {
                this.fileSelectedSubject.next({ id, fileData: base64 });
              })
              .catch((error) => {
                console.error('Error saving image to IndexedDB:', error);
                this.fileSelectedSubject.next({ id: 0, fileData: null });
              });
          };
          img.src = base64;
        };
        reader.readAsDataURL(file);
      }

      input.value = '';
    }
  }

  displayImage(base64: string, containerId: string): void {
    const imgElement = document.createElement('img');
    imgElement.src = base64;
    imgElement.alt = 'Selected Image';
    imgElement.style.maxWidth = '200px';
    imgElement.style.maxHeight = '200px';

    const container = document.getElementById(containerId);
    if (container) {
      container.innerHTML = '';
      container.appendChild(imgElement);
    }
  }
}
