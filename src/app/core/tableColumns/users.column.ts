import { cloneDeep } from 'lodash';
import { STRING_CASE_INSENSITIVE_SORT_FN } from '../constants/functions';
import { ITableColumn } from '../interface/table';
import { IUserFields } from '../interface/user-fields';

export const USERS_TABLE_COLUMNS: ITableColumn<IUserFields>[] = [
  {
    header: 'User Name',
    field: 'profileandimage',
    fieldType: 'profile',
    profileImageField: 'profilePictureUrl',
    fullNameField: 'fullName',
    width: '20rem',
    sortFn: (a, b) => STRING_CASE_INSENSITIVE_SORT_FN(a.firstName, b.firstName),
  },
  {
    header: 'Email',
    field: 'email',
    fieldType: 'text',
    width: '20rem',
  },
  {
    header: 'Contact',
    field: 'contactNumber',
    fieldType: 'text',
    width: '20rem',
  },

  {
    header: 'Role',
    field: 'roledisplaytext',
    fieldType: 'text',
    width: '20rem',
  },
];

const TOGGLE_BUTTON: ITableColumn<IUserFields>[] = [
  {
    header: 'Action',
    field: 'action',
    fieldType: 'actionFields',
    width: '20rem',
    actionFieldColumns: [
      {
        field: 'toggle',
        header: '',
        fieldType: 'toggle',
      },
    ],
  },
];

const EDIT_DELETE_BUTTONS: ITableColumn<IUserFields>[] = [
  {
    header: 'Action',
    field: 'action',
    fieldType: 'actionFields',
    width: '20rem',
    actionFieldColumns: [
      {
        field: 'edit',
        header: '',
        fieldType: 'icon',
        iconType: 'nzIcon',
        nzIconType: 'edit',
        nzIconTheme: 'outline',
        showTooltip: true,
        iconTooltipTitle: 'Edit User',
      },
      {
        field: 'delete',
        header: '',
        fieldType: 'icon',
        iconType: 'nzIcon',
        nzIconType: 'delete',
        nzIconTheme: 'outline',
        showTooltip: true,
        iconTooltipTitle: 'Delete User',
      },
    ],
  },
];

const EDIT_TOGGLE_BUTTONS: ITableColumn<IUserFields>[] = [
  {
    header: 'Action',
    field: 'action',
    fieldType: 'actionFields',
    width: '10rem',
    actionFieldColumns: [
      {
        field: 'edit',
        header: '',
        fieldType: 'icon',
        iconType: 'nzIcon',
        nzIconType: 'edit',
        nzIconTheme: 'outline',
        showTooltip: true,
        iconTooltipTitle: 'Edit User',
        conditionForShowActionField: (rowData: IUserFields) =>
          rowData.isActive === true,
      },
      {
        field: 'toggle',
        header: '',
        fieldType: 'toggle',
      },
    ],
  },
];

export const MANAGE_USER_COLUMNS: ITableColumn<IUserFields>[] = [
  {
    header: 'User',
    field: 'profileWithTextCaption',
    fieldType: 'profileWithTextCaption',
    profileImageField: 'profilePictureUrl',
    width: '17rem',
    getText: (rowData: IUserFields) => rowData.fullName || '',
    getCaption: (rowData: IUserFields) => rowData.email || '',
  },
  {
    header: 'Role',
    field: 'roleDisplayText',
    fieldType: 'text',
    width: '8rem',
  },
];

export const ADD_DELETE_USER_TABLE_COLUMNS = cloneDeep([
  ...USERS_TABLE_COLUMNS,
  ...EDIT_DELETE_BUTTONS,
]);

export const VIEW_USER_TABLE_COLUMNS = cloneDeep([
  ...USERS_TABLE_COLUMNS,
  ...TOGGLE_BUTTON,
]);

export const MANAGE_USER_TABLE_COLUMNS = cloneDeep([
  ...MANAGE_USER_COLUMNS,
  ...EDIT_TOGGLE_BUTTONS,
]);
