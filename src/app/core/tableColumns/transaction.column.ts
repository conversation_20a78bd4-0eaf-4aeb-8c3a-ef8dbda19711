import {
  DATE_SORT_FN,
  NUM_SORT_FN,
  STRING_CASE_INSENSITIVE_SORT_FN,
} from '../constants/functions';

import { ITableColumn } from '../interface/table';
import { ITransaction } from '../interface/transaction.interface';

export const TRANSACTION_TABLE_COLUMNS: ITableColumn<ITransaction>[] = [
  {
    header: 'Purchased Product',
    field: 'productName',
    fieldType: 'text',
    width: '25rem',

    altText: 'N/A',
    conditionForAltText: (rowData) => !rowData.productName,
    sortFn: (a, b) =>
      STRING_CASE_INSENSITIVE_SORT_FN(a.productName, b.productName),
  },

  {
    header: 'Issue Date',
    field: 'transactionDate',
    fieldType: 'dateField',
    width: '15rem',
    altText: 'N/A',
    conditionForAltText: (rowData) => !rowData.price,
    sortFn: (a, b) => DATE_SORT_FN(a.transactionDate, b.transactionDate),
  },
  {
    header: 'Expiry Date',
    field: 'expiryDate',
    fieldType: 'dateField',
    width: '15rem',
    altText: 'N/A',
    conditionForAltText: (rowData) => !rowData.expiryDate,
    sortFn: (a, b) => DATE_SORT_FN(a.expiryDate, b.expiryDate),
  },
  {
    header: 'Product Price',
    field: 'finalPrice',
    fieldType: 'text',
    width: '15rem',
    sortFn: (a, b) => NUM_SORT_FN(a.price, b.price),
  },
  {
    header: 'Status',
    field: 'status',
    fieldType: 'text',
    width: '15rem',
  },
  {
    header: 'Action',
    field: 'action',
    fieldType: 'actionFields',
    width: '7rem',
    actionFieldColumns: [
      {
        field: 'view',
        header: '',
        fieldType: 'icon',
        iconClasses: ['fa', 'fa-eye', 'blue-info-icon'],
        btnLabelFn: () => 'View',
      },
      {
        field: 'Download',
        header: '',
        fieldType: 'icon',
        iconClasses: ['fa', 'fa-download', 'blue-info-icon'],
        btnLabelFn: () => 'Download',
      },
    ],
  },
];
