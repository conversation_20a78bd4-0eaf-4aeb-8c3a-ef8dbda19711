import { cloneDeep } from 'lodash';
import {
  NUM_SORT_FN,
  STRING_CASE_INSENSITIVE_SORT_FN,
} from '../constants/functions';
import {
  IPricingFields,
  IPricingViewDetails,
} from '../interface/pricing-fields';
import { ITableColumn } from '../interface/table';

export const PRICING_MANAGEMENT_TABLE_COLUMNS: ITableColumn<IPricingFields>[] =
  [
    {
      header: 'Document Name',
      field: 'name',
      fieldType: 'text',
      width: '15rem',
      sortFn: (a, b) => STRING_CASE_INSENSITIVE_SORT_FN(a.name, b.name),
    },
    {
      header: 'Description',
      field: 'description',
      fieldType: 'text',
      width: '15rem',
    },
    {
      header: 'Cost',
      field: 'basePrice',
      fieldType: 'currency',
      width: '12rem',
      sortFn: (a, b) => NUM_SORT_FN(a.basePrice, b.basePrice),
    },
  ];

const TOTAL_PRICE: ITableColumn<IPricingFields>[] = [
  {
    header: 'Product Price (In GST)',
    field: 'productPriceIncGst',
    fieldType: 'currency',
    width: '12rem',
    sortFn: (a, b) => NUM_SORT_FN(a.productPriceIncGst, b.productPriceIncGst),
  },
];

const AREALYTICS_PRICE: ITableColumn<IPricingFields>[] = [
  {
    header: 'Arealytics Price (Ex GST)',
    field: 'specialPrice',
    fieldType: 'inputFieldWithIcon',
    isEditable: true,
    width: '12rem',
    sortFn: (a, b) => NUM_SORT_FN(a.effectiveBasePrice, b.effectiveBasePrice),
    placeholder: 'Enter price...',
    iconClass: 'save',
    saveAction: true,
  },
];

const PRODUCT_PRICE_AND_GST: ITableColumn<IPricingFields>[] = [
  {
    header: 'Product Price (Ex GST)',
    field: 'effectiveBasePrice',
    fieldType: 'inputFieldWithIcon',
    isEditable: true,
    width: '12rem',
    sortFn: (a, b) => NUM_SORT_FN(a.effectiveBasePrice, b.effectiveBasePrice),
    placeholder: 'Enter price...',
    iconClass: 'save',
    saveAction: true,
  },
  {
    header: 'GST',
    field: 'effectivePriceGst',
    fieldType: 'currency',
    width: '12rem',
    sortFn: (a, b) => NUM_SORT_FN(a.effectivePriceGst, b.effectivePriceGst),
  },
];

const VIEW_PRODUCT_PRICE: ITableColumn<IPricingFields>[] = [
  {
    header: 'Product Price (Ex GST)',
    field: 'effectiveBasePrice',
    fieldType: 'currency',
    isEditable: true,
    width: '12rem',
    sortFn: (a, b) => NUM_SORT_FN(a.effectiveBasePrice, b.effectiveBasePrice),
  },
];

const EDIT_DELETE_BUTTONS: ITableColumn<IPricingFields>[] = [
  {
    header: 'Action',
    field: 'action',
    fieldType: 'actionFields',
    width: '6rem',
    actionFieldColumns: [
      {
        field: 'edit',
        header: '',
        fieldType: 'icon',
        iconType: 'nzIcon',
        nzIconType: 'edit',
        nzIconTheme: 'outline',
        showTooltip: true,
        iconTooltipTitle: 'Edit Document',
      },
      {
        field: 'delete',
        header: '',
        fieldType: 'icon',
        iconType: 'nzIcon',
        nzIconType: 'delete',
        nzIconTheme: 'outline',
        showTooltip: true,
        iconTooltipTitle: 'Delete Document',
      },
    ],
  },
];

export const PRICING_MANAGEMENT_COMPANY_LEVEL_TABLE_COLUMNS: ITableColumn<IPricingFields>[] =
  cloneDeep([
    ...PRICING_MANAGEMENT_TABLE_COLUMNS,
    ...VIEW_PRODUCT_PRICE,
    ...AREALYTICS_PRICE,
    ...TOTAL_PRICE,
  ]);

export const PRICING_MANAGEMENT_GLOBAL_LEVEL_TABLE_COLUMNS: ITableColumn<IPricingFields>[] =
  cloneDeep([
    ...PRICING_MANAGEMENT_TABLE_COLUMNS,
    ...PRODUCT_PRICE_AND_GST,
    ...TOTAL_PRICE,
    ...EDIT_DELETE_BUTTONS,
  ]);

export const DUMMY_PRICING_VIEW_DATA: IPricingViewDetails[] = [
  {
    documentPrice: 100.0,
    dateTime: '2023-05-30',
    updatedBy: 'John Doe',
    billingEmail: '<EMAIL>',
  },
  {
    documentPrice: 120.0,
    dateTime: '2023-06-01',
    updatedBy: 'Jane Smith',
    billingEmail: '<EMAIL>',
  },
  {
    documentPrice: 150.0,
    dateTime: '2023-06-02',
    updatedBy: 'Bob Johnson',
    billingEmail: '<EMAIL>',
  },
  {
    documentPrice: 180.0,
    dateTime: '2023-06-03',
    updatedBy: 'Alice Brown',
    billingEmail: '<EMAIL>',
  },
  {
    documentPrice: 200.0,
    dateTime: '2023-06-04',
    updatedBy: 'Tom Wilson',
    billingEmail: '<EMAIL>',
  },
];

export const VIEW_PRICING_DETAILS_COLUMNS: ITableColumn<IPricingViewDetails>[] =
  [
    {
      header: 'Doc Price',
      field: 'documentPrice',
      fieldType: 'currency',
      width: '5rem',
    },
    {
      header: 'Date & Time',
      field: 'dateTime',
      fieldType: 'text',
      width: '5rem',
    },
    {
      header: 'Updated By',
      field: 'updatedBy',
      fieldType: 'textWithCaption',
      width: '10rem',
      getText: (rowData: IPricingViewDetails) => rowData.updatedBy || 'N/A',
      getCaption: (rowData: IPricingViewDetails) =>
        rowData.billingEmail || 'N/A',
    },
  ];
