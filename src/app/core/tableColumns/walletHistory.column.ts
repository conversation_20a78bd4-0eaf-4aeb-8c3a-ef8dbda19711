import { TransactionDTO } from '../../api-client';

import { ITableColumn } from '../interface/table';

export const WALLET_HISTORY_TABLE_COLUMNS: ITableColumn<TransactionDTO>[] = [
  {
    header: 'Transaction ID',
    field: 'transactionId',
    fieldType: 'text',
    width: '10rem',
  },

  {
    header: 'Date & Time',
    field: 'transactionDate',
    fieldType: 'dateTime',
    width: '10rem',
    altText: 'N/A',
  },
  {
    header: 'Amount',
    field: 'amount',
    fieldType: 'text',
    width: '5rem',
    altText: 'N/A',
  },
];
