import {
  STRING_CASE_INSENSITIVE_SORT_FN,
  NUM_SORT_FN,
} from '../constants/functions';
import { ISearchData } from '../interface/search-base.interface';
import { ITableColumn } from '../interface/table';

export const SEARCH_TABLE_COLUMNS: ITableColumn<ISearchData>[] = [
  {
    header: 'Document',
    field: 'productName',
    fieldType: 'text',
    width: '25rem',
    sortFn: (a, b) =>
      STRING_CASE_INSENSITIVE_SORT_FN(a.productName, b.productName),
    iconClasses: ['fa', 'fa-info-circle', 'mx-2', 'blue-info-icon'],
    conditionForShowIcon: (rowData) => !!rowData.productDescription,
    getIconToolTipTextFn: (rowData) => rowData.productDescription || null,
  },

  {
    header: 'Price (EX GST)',
    field: 'price',
    fieldType: 'text',
    width: '15rem',
    sortFn: (a, b) => NUM_SORT_FN(a.price, b.price),
  },
  {
    header: 'GST',
    field: 'gst',
    fieldType: 'text',
    width: '15rem',
    sortFn: (a, b) => NUM_SORT_FN(a.gst, b.gst),
  },
  {
    header: 'Delivery Time',
    field: 'deliveryTime',
    fieldType: 'text',
    width: '15rem',
    sortFn: (a, b) => NUM_SORT_FN(a.gst, b.gst),
  },

  {
    header: 'Action',
    field: 'action',
    fieldType: 'actionFields',
    width: '15rem',
    actionFieldColumns: [
      {
        field: 'addToCart',
        header: '',
        fieldType: 'icon',
        iconType: 'svgIcon',
        badgeIconType: 'nzIcon',
        nzBadgeIconType: 'close-circle',
        nzBadgeIconTheme: 'fill',
      },
    ],
  },
];
