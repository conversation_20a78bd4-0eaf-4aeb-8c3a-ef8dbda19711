import { INavItem } from '../interface/nav-item';

export const HeaderNavItems: INavItem[] = [
  { label: 'Home', route: 'home', requiresAuth: false },
  { label: 'Products', route: 'products', requiresAuth: false },
  { label: 'Contact', route: 'contact', requiresAuth: false },
  { label: 'Register', route: 'register', requiresAuth: false },
  {
    label: 'Login',
    route: 'login',
    requiresAuth: false,
    showIcon: true,
    iconName: 'user',
  },

  // Items visible only when logged in
  { label: 'Home', route: 'homepage', requiresAuth: true },
  { label: 'Folders', route: 'folders', requiresAuth: true },
  { label: 'My Transactions', route: 'purchases', requiresAuth: true },
  { label: 'Reports', route: 'reports', requiresAuth: true },
  {
    label: 'Account',
    route: 'account',
    requiresAuth: true,
    showIcon: true,
    iconName: 'user',
  },
  { label: 'Logout', route: 'logout', requiresAuth: true },
];
