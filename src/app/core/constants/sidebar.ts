import { EnumUserRole } from '../enumerations/user-roles';
import { ISidebarItem } from '../interface/sidebar';
import { ROUTES } from './routes';

export const SIDEBAR_MENU_ITEMS: ISidebarItem[] = [
  // Platform Admin
  // commenting for now
  // {
  //   label: 'Dashboard View',
  //   icon: 'dashboard',
  //   route: ROUTES.sidebar.dashboard,
  //   hasArrow: true,
  //   roleIds: [EnumUserRole.PLATFORM_ADMIN],
  // },
  {
    label: 'Pricing Management',
    icon: 'tag',
    route: ROUTES.sidebar.pricing,
    roleIds: [EnumUserRole.PLATFORM_ADMIN],
  },
  {
    label: 'Company Management',
    icon: 'tag',
    route: ROUTES.sidebar.company,
    roleIds: [EnumUserRole.PLATFORM_ADMIN],
  },

  {
    label: 'Search',
    icon: 'search',
    route: ROUTES.sidebar.homepage,
    roleIds: Object.values(EnumUserRole).filter(
      (role) => role != EnumUserRole.PLATFORM_ADMIN,
    ) as EnumUserRole[],
  },

  {
    label: 'My Transactions',
    icon: 'shopping-cart',
    route: ROUTES.sidebar.purchases,
    roleIds: Object.values(EnumUserRole).filter(
      (role) => role != EnumUserRole.PLATFORM_ADMIN,
    ) as EnumUserRole[],
  },
  {
    label: 'My Favorites',
    icon: 'star',
    route: ROUTES.sidebar.favorites,
    roleIds: Object.values(EnumUserRole).filter(
      (role) => role != EnumUserRole.PLATFORM_ADMIN,
    ) as EnumUserRole[],
  },
  {
    label: 'Notifications',
    icon: 'bell',
    route: ROUTES.sidebar.notifications,
    roleIds: Object.values(EnumUserRole).filter(
      (role) => role != EnumUserRole.PLATFORM_ADMIN,
    ) as EnumUserRole[],
  },
];
