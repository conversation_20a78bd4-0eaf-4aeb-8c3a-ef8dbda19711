<div class="recharge-section">
  <!-- Drawer Header -->

  <!-- Recharge Form -->
  <div class="drawer-body recharge-container">
    <label class="field-label">Enter Amount</label>

    <nz-input-number
      class="recharge-input"
      [(ngModel)]="amount"
      (ngModelChange)="amountChange.emit(amount)"
    >
      <span nzInputPrefix>$</span>
    </nz-input-number>

    <!-- Preset Buttons -->
    <div class="preset-buttons">
      <button
        *ngFor="let preset of presetAmounts"
        nz-button
        nzType="default"
        class="preset-button"
        (click)="setPresetAmount(preset)"
      >
        {{ preset | currency: 'USD' }}
      </button>
    </div>

    <!-- Confirm & Cancel -->
    <div class="action-buttons">
      <button
        nz-button
        nzType="primary"
        block
        class="confirm-button"
        (click)="confirmRecharge()"
      >
        Recharge Wallet
      </button>

      <button
        nz-button
        nzType="primary"
        nzGhost
        block
        class="cancel-button"
        (click)="onCancel()"
      >
        Cancel
      </button>
    </div>
  </div>
</div>
<div class="recharge-history-container">
  <app-dropdown-grid
    class="recharge-history"
    [visibleInstrumentDrawer]="instrumentDrawerVisible"
    (visibleInstrumentDrawerChange)="instrumentDrawerVisible = $event"
  ></app-dropdown-grid>
</div>
