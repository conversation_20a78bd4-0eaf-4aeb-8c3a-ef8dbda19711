import { ComponentFixture, TestBed } from '@angular/core/testing';

import { HttpClientTestingModule } from '@angular/common/http/testing';

import { WalletComponent } from './wallet.component';
import { RechargeService } from '../../services/recharge.service';
import { NotificationService } from '../../services/notification.service';
import { UserService } from '../../services/user.service';
import { Router } from '@angular/router';
import { of, throwError } from 'rxjs';

describe('WalletComponent', () => {
  let component: WalletComponent;
  let fixture: ComponentFixture<WalletComponent>;
  let rechargeServiceSpy: jasmine.SpyObj<RechargeService>;
  let notificationServiceSpy: jasmine.SpyObj<NotificationService>;
  let userServiceSpy: jasmine.SpyObj<UserService>;
  let routerSpy: jasmine.SpyObj<Router>;

  // Utility function to set private userId
  const setUserId = (value: number | undefined) => {
    Object.defineProperty(component, 'userId', {
      value,
      writable: true,
    });
  };

  beforeEach(async () => {
    rechargeServiceSpy = jasmine.createSpyObj('RechargeService', [
      'rechargeWallet',
    ]);
    notificationServiceSpy = jasmine.createSpyObj('NotificationService', [
      'showLoaderMessage',
      'hideLoaderMessage',
      'error',
    ]);
    userServiceSpy = jasmine.createSpyObj('UserService', ['getUserRole'], {
      userInfo: { firstName: 'TestUser' },
    });
    routerSpy = jasmine.createSpyObj('Router', ['navigate'], {
      url: '/wallet',
    });

    await TestBed.configureTestingModule({
      imports: [WalletComponent, HttpClientTestingModule],
      providers: [
        { provide: RechargeService, useValue: rechargeServiceSpy },
        { provide: NotificationService, useValue: notificationServiceSpy },
        { provide: UserService, useValue: userServiceSpy },
        { provide: Router, useValue: routerSpy },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(WalletComponent);
    component = fixture.componentInstance;
  });

  it('should create component', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize user on ngOnInit', async () => {
    userServiceSpy.getUserRole.and.resolveTo({ id: 1 });
    await component.ngOnInit();
    expect(userServiceSpy.getUserRole).toHaveBeenCalled();
  });

  it('should emit amount on setPresetAmount()', () => {
    const spy = spyOn(component.amountChange, 'emit');
    component.setPresetAmount(100);
    expect(spy).toHaveBeenCalledWith(100);
    expect(component.amount).toBe(100);
  });

  it('should toggle instrumentDrawerVisible flag', () => {
    expect(component.instrumentDrawerVisible).toBeFalse();
    component.toggleRechargeHistory();
    expect(component.instrumentDrawerVisible).toBeTrue();
  });

  it('should emit onCancel signals', () => {
    const delaySpy = spyOn(component.visibleWalletAfterDelay, 'emit');
    const changeSpy = spyOn(component.visibleWalletChange, 'emit');

    component.onCancel();

    expect(delaySpy).toHaveBeenCalledWith(10000);
    expect(changeSpy).toHaveBeenCalledWith(false);
  });

  it('should return current user name from userService', () => {
    expect(component.getCurrentUser()).toBe('TestUser');
  });

  it('should skip recharge if not authenticated', () => {
    setUserId(undefined);
    component.confirmRecharge();
    expect(notificationServiceSpy.error).toHaveBeenCalled();
  });

  it('should call rechargeWallet on confirmRecharge()', () => {
    setUserId(42);
    component.amount = 10;
    rechargeServiceSpy.rechargeWallet.and.returnValue(of({ newBalance: 100 }));
    notificationServiceSpy.showLoaderMessage.and.returnValue('msg1');

    component.confirmRecharge();

    expect(rechargeServiceSpy.rechargeWallet).toHaveBeenCalledWith(42, 10);
    expect(notificationServiceSpy.hideLoaderMessage).toHaveBeenCalledWith(
      'msg1',
    );
  });

  it('should handle rechargeWallet error', () => {
    setUserId(99);
    component.amount = 25;
    notificationServiceSpy.showLoaderMessage.and.returnValue('err123');
    rechargeServiceSpy.rechargeWallet.and.returnValue(
      throwError(() => new Error('error')),
    );

    component.confirmRecharge();

    expect(notificationServiceSpy.error).toHaveBeenCalled();
    expect(notificationServiceSpy.hideLoaderMessage).toHaveBeenCalledWith(
      'err123',
    );
  });
});
