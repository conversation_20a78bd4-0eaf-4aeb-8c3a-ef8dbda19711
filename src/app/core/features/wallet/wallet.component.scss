.recharge-section {
  margin-top: 0;
  padding: 0;
  max-width: 100%;
  overflow: hidden;
  animation: slideInRight 0.3s ease-in-out;
  display: flex;
  flex-direction: column;
  min-height: 100%; // Full height to position history at bottom
  position: relative;
}

@keyframes slideInRight {
  from {
    transform: translateX(0);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.drawer-body {
  margin-top: 0;
  background-color: #ffffff;
  flex: 1; // Take remaining space
  display: flex;
  flex-direction: column;
}

.recharge-container {
  margin-top: 0;
  display: flex;
  flex-direction: column;
  gap: 16px;
  flex: 1; // Take available space
}

.field-label {
  font-weight: 600;
  font-size: 14px;
  color: #333;
}

.preset-buttons {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
  width: 100%;

  .preset-button {
    border-radius: 8px;
    background-color: #f0f2f5;
    border: none;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    padding: 8px 12px;
    text-align: center;
    min-height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      background-color: #e6f7ff;
      border: 1px solid #91d5ff;
    }
  }
}

.recharge-input {
  width: 100%;
  margin: 12px 0;
}

.action-buttons {
  display: flex;
  gap: 12px;
  width: 100%;
}

.confirm-button {
  border-radius: 8px;
  height: 40px;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  flex: 1;
}

.cancel-button {
  border-radius: 8px;
  height: 40px;
  flex: 1;
}

.recharge-history-container {
  position: absolute;
  bottom: 0;
  min-width: 100%;
  left: 0;
  right: 0;
  z-index: 100;
}

@media (max-width: 768px) {
  .preset-buttons {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }

  .drawer-header {
    padding: 16px;

    .user-info {
      .wallet-icon {
        font-size: 24px;
      }
    }
  }

  .recharge-section {
    min-height: 100%; // Maintain full height on mobile
  }
}

@media (max-width: 480px) {
  .preset-buttons {
    grid-template-columns: 1fr;
    gap: 8px;
  }
}
