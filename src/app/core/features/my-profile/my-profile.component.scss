.profile-section {
  padding: 12px;
  max-width: 100%;
  overflow: hidden;
}

.avatar-wrapper {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 16px;
}

.avatar-container {
  position: relative;
  display: inline-block;
  
  .camera-button {
    position: absolute;
    bottom: 8px;
    right: 8px;
    background-color: white;
    box-shadow: 0 2px 8px rgba(0, 0, 1, 0.15);
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
  }
}

.action-buttons {
  display: flex;
  gap: 10px;
  margin-bottom: 16px;
  justify-content: flex-end;
}

.info-card {
  background: #EBF4FD;
  margin-bottom: 16px;
  border-radius: 10px;
  padding: 1rem;
  position: relative;
  
  .edit-icon {
    position: absolute;
    top: 8px;
    right: 8px;
    background: transparent;
    border: none;
    color: #195FAC;
    font-size: 18px !important;
    
    &:hover {
      color: #195FAC;
      background: transparent;
    }
    
    &:focus {
      color: #195FAC;
      background: transparent;
    }
  }
  
  
  /* View mode styling */
  p {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    strong {
      margin-right: 20px;
      min-width: 120px;
      color: #6E6E6E;
      font-weight: normal;
    }
  }
  
  /* Make the actual values (text after strong) bold */
  p {
    color:#040404;
    
    strong {
      font-weight: normal;
    }
  }
  
  .edit-form {
    padding-top: 8px;
    
    .form-field {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 8px;  
    }
    
    .field-label {
      font-weight: normal; 
      font-size: 14px;
      color: #6E6E6E;  
      min-width: 120px;  
      margin-bottom: 4px;
      margin-right: 20px; 
    }
    
    .edit-input {
      flex: 1;
      border-radius: 6px;
      border: 1px solid #d9d9d9;
      padding: 8px 12px;
      font-size: 14px;
      color: #040404;  
      
      &:focus {
        border-color: #1890ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
      }
      
      &:disabled {
        background-color: #f5f5f5;
        color: #999;
      }
    }
    
    .edit-actions {
      display: flex;
      gap: 8px;
      justify-content: flex-start;
      margin-top: 20px;
      padding-top: 16px;
      border-top: 1px solid #e8e8e8;
    }
  }
}

.wallet-card {
  background:#EBF4FD;
  border-radius: 10px;
  padding: 0.50rem;
  
  .recharge-button {
    font-weight: 500;
  }
  
  .wallet-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    
    .col {
      width: 100%;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
  
  .muted {
    color: #6E6E6E;
    margin-bottom: 4px;
  }
}

.recharge-section {
  padding: 0;
  max-width: 100%;
  overflow: hidden;
  animation: slideInRight 0.3s ease-in-out;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.drawer-breadcrumb {
  background: #f8f9fa;
  padding: 16px 24px;
  border-bottom: 1px solid #e8e8e8;
  
  ::ng-deep .ant-breadcrumb {
    font-size: 14px;
    
    .ant-breadcrumb-link {
      color: #666;
      cursor: pointer;
      
      &:hover {
        color: #1890ff;
      }
      
      i {
        margin-right: 6px;
      }
    }
    
    .ant-breadcrumb-separator {
      color: #999;
    }
  }
}

.drawer-body {
  padding: 24px;
  background-color: #FDFEFF;
}

@media (max-width: 768px) {
  .drawer-header {
    padding: 16px;
  }
  
  .drawer-breadcrumb {
    padding: 8px 12px;
  }
  
  .info-card .edit-form .edit-actions {
    flex-direction: column;
    
    button {
      width: 100%;
      justify-content: center;
    }
  }
}

.more-details-toggle {
  display: flex;
  align-items: center;
  justify-content: center;

  .toggle-text {
    margin-right: 8px;
    font-size: 14px;
    color:#195FAC;
  }

  .toggle-icon {
    font-size: 14px;
    color: #195FAC;
  }
}
