<div class="mock-recharge">
  <div *ngIf="currentState === 'PROCESSING'" class="backDropWrapper">
    <nz-spin nzSize="large" nzTip="Processing your recharge...">
      <nz-card [nzBordered]="false" class="status-card">
        <div class="icon-wrapper">
          <i
            nz-icon
            nzType="credit-card"
            nzTheme="outline"
            class="payment-icon"
          ></i>
        </div>
        <h2 class="status-title">Processing Recharge</h2>
        <p class="status-subtitle">
          Please wait while we process your transaction
        </p>
        <div class="amount-display">${{ paymentAmount }}</div>
      </nz-card>
    </nz-spin>
  </div>

  <div *ngIf="currentState === 'SUCCESS'" class="backDropWrapper">
    <nz-result
      nzStatus="success"
      nzTitle="Recharge Successful!"
      nzSubTitle="Your wallet has been recharged successfully."
    >
      <div nz-result-content>
        <nz-card [nzBordered]="false" class="status-card">
          <div class="info-row">
            <span class="label">Amount Recharged:</span>
            <span class="value">${{ paymentAmount }}</span>
          </div>
        </nz-card>
      </div>
    </nz-result>
  </div>
</div>
