import { inject, Injectable } from '@angular/core';
import { map, Observable } from 'rxjs';
import {
  ApiResponseUserResponseDTO,
  UserControllerService,
  UserRequestDTO,
  UserResponseDTO,
  CompanyControllerService,
  ApiResponsePageUserResponseDTO,
} from '../../api-client';
import { EnumUserRole } from '../enumerations/user-roles';
import { ROUTES } from '../constants/routes';
import { ICompanyFields } from '../interface/company-fields';

@Injectable({
  providedIn: 'root',
})
export class UserService {
  private _userInfo?: UserResponseDTO;
  private _companyInfo?: ICompanyFields | null;
  private userControllerService = inject(UserControllerService);
  private companyControllerService = inject(CompanyControllerService);

  async getUserRole(): Promise<UserResponseDTO | undefined> {
    if (!this._userInfo) {
      try {
        const userResponse = await this.userControllerService
          .getCurrentUser()
          .toPromise();
        this._userInfo = userResponse?.data;

        if (this._userInfo?.companyId) {
          const companyResponse = await this.companyControllerService
            .getCompanies(this._userInfo.companyId)
            .toPromise();
          this._companyInfo = companyResponse?.data?.content?.[0];
        }
      } catch (error) {
        console.error('Error fetching user or company info', error);
        this._userInfo = undefined;
        this._companyInfo = undefined;
      }
    }

    return this._userInfo;
  }

  async getCompanyAdminId(companyId: number): Promise<number | undefined> {
    try {
      const usersResponse = await this.userControllerService
        .getUsers(undefined, undefined, companyId, undefined, undefined, 200)
        .pipe(map((res: ApiResponsePageUserResponseDTO) => res.data?.content))
        .toPromise();
      const companyAdmin = usersResponse?.find(
        (user) => user.roleId === EnumUserRole.COMPANY_ADMIN,
      );
      return companyAdmin?.id;
    } catch (error) {
      console.error('Error fetching company admin:', error);
      return undefined;
    }
  }

  getPrimaryNativeRouteBasedOnUserRole(roleId: EnumUserRole) {
    switch (roleId) {
      case EnumUserRole.PLATFORM_ADMIN:
        return ROUTES.sidebar.company;
      case EnumUserRole.COMPANY_ADMIN:
      case EnumUserRole.COMPANY_MEMBER:
      case EnumUserRole.INDEPENDEDNT_AGENT:
        return ROUTES.sidebar.homepage;
      default:
        return ROUTES.auth.login;
    }
  }

  get userInfo() {
    return this._userInfo;
  }

  set userInfo(userInfo: UserResponseDTO | undefined) {
    this._userInfo = userInfo;
  }

  get companyInfo() {
    return this._companyInfo;
  }

  set companyInfo(companyInfo: ICompanyFields | null | undefined) {
    this._companyInfo = companyInfo;
  }

  removeUserInfo() {
    this._userInfo = undefined;
    this._companyInfo = undefined;
  }
  updateUser(userId: number, userRequestDTO: UserRequestDTO) {
    return this.userControllerService.updateUser(userId, userRequestDTO);
  }

  public changePassword(email: string): Observable<ApiResponseUserResponseDTO> {
    return this.userControllerService.resetPassword(email);
  }
}
