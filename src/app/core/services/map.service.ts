import { inject, Injectable, NgZone } from '@angular/core';
import { NotificationService } from './notification.service';
import * as MapEnum from '../enumerations/mapEnum';
import { MapOptions } from '../models/mapOptions';
import { MapBound } from '../models/mapBound';
import { LatLng } from '../models/latLng';

import { Loader } from '@googlemaps/js-api-loader';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root',
})
export class MapService {
  private googleMapsLoaded: Promise<void>;
  private google!: typeof google; // Not ideal for google maps
  private zone = inject(NgZone);
  private notificationService = inject(NotificationService);

  constructor() {
    const loader = new Loader({
      apiKey: environment.GoogleMapApiKey,
      libraries: ['marker', 'drawing', 'geometry', 'places'],
    });

    this.googleMapsLoaded = loader.load().then(() => {
      this.google = window['google'];
    });
  }

  private async getPositionValue(position: MapEnum.GoogleMapControlPosition) {
    await this.googleMapsLoaded;
    switch (position) {
      case MapEnum.GoogleMapControlPosition.Top_Center:
        return this.google.maps.ControlPosition.TOP_CENTER;
      case MapEnum.GoogleMapControlPosition.Top_Left:
        return this.google.maps.ControlPosition.TOP_LEFT;
      case MapEnum.GoogleMapControlPosition.Top_Right:
        return this.google.maps.ControlPosition.TOP_RIGHT;
      case MapEnum.GoogleMapControlPosition.LEFT_Top:
        return this.google.maps.ControlPosition.LEFT_TOP;
      case MapEnum.GoogleMapControlPosition.Right_Top:
        return this.google.maps.ControlPosition.RIGHT_TOP;
      case MapEnum.GoogleMapControlPosition.Left_Center:
        return this.google.maps.ControlPosition.LEFT_CENTER;
      case MapEnum.GoogleMapControlPosition.Right_Center:
        return this.google.maps.ControlPosition.RIGHT_CENTER;
      case MapEnum.GoogleMapControlPosition.Left_Bottom:
        return this.google.maps.ControlPosition.LEFT_BOTTOM;
      case MapEnum.GoogleMapControlPosition.Right_Bottom:
        return this.google.maps.ControlPosition.RIGHT_BOTTOM;
      case MapEnum.GoogleMapControlPosition.Bottom_Center:
        return this.google.maps.ControlPosition.BOTTOM_CENTER;
      case MapEnum.GoogleMapControlPosition.Bottom_Left:
        return this.google.maps.ControlPosition.BOTTOM_LEFT;
      case MapEnum.GoogleMapControlPosition.Bottom_Right:
        return this.google.maps.ControlPosition.BOTTOM_RIGHT;
      default:
        return this.google.maps.ControlPosition.TOP_RIGHT;
    }
  }

  private async generateMapOptionsForGoogle(mapOptions: MapOptions) {
    const result: google.maps.MapOptions = {};
    result.center = await this.GetLatLng(
      mapOptions.CenterLat,
      mapOptions.CenterLng,
    );
    result.mapId = mapOptions.MapId;
    // 'draggable' is deprecated; use 'gestureHandling' instead.
    if (mapOptions.IsDraggable === false) {
      result.gestureHandling = 'none';
    } else if (!mapOptions.RequireCtrlToZoom) {
      result.gestureHandling = 'greedy';
    } else {
      result.gestureHandling = 'cooperative';
    }
    if (mapOptions.FullscreenControl != null) {
      result.fullscreenControl = mapOptions.FullscreenControl;
    }
    result.mapTypeId = await this.GetMapTypeId(mapOptions.MapType);
    result.zoom = mapOptions.ZoomLevel;
    result.maxZoom = mapOptions.MaxZoom;
    result.minZoom = mapOptions.MinZoom;
    return result;
  }

  private getFeatureText(feature: MapEnum.MapFeatures) {
    switch (feature) {
      case MapEnum.MapFeatures.Administrative_Country:
        return 'administrative.country';
      case MapEnum.MapFeatures.Administrative_LandParcel:
        return 'administrative.land_parcel';
      case MapEnum.MapFeatures.Administrative_Locality:
        return 'administrative.locality';
      case MapEnum.MapFeatures.Administrative_Neighborhood:
        return 'administrative.neighborhood';
      case MapEnum.MapFeatures.Administrative_Province:
        return 'administrative.province';
      case MapEnum.MapFeatures.ManMadeLandscape:
        return 'administrative.province';
      case MapEnum.MapFeatures.NaturalLandscape_Landcover:
        return 'landscape.natural.landcover';
      case MapEnum.MapFeatures.NaturalLandscape_Terrain:
        return 'landscape.natural.terrain';
      case MapEnum.MapFeatures.AttractionPin:
        return 'poi.attraction';
      case MapEnum.MapFeatures.BusinessPin:
        return 'poi.business';
      case MapEnum.MapFeatures.GovernmentPin:
        return 'poi.government';
      case MapEnum.MapFeatures.MedicalInstitutionPin:
        return 'poi.medical';
      case MapEnum.MapFeatures.ParkPin:
        return 'poi.park';
      case MapEnum.MapFeatures.PlaceOfWorkshipPin:
        return 'poi.place_of_worship';
      case MapEnum.MapFeatures.ScoolPin:
        return 'poi.school';
      case MapEnum.MapFeatures.SportsComplexPin:
        return 'poi.sports_complex';
      case MapEnum.MapFeatures.ArterialRoad:
        return 'road.arterial';
      case MapEnum.MapFeatures.HighwayRoad:
        return 'road.highway';
      case MapEnum.MapFeatures.ControlledAccessHighwayRoad:
        return 'road.highway.controlled_access';
      case MapEnum.MapFeatures.LocalRoad:
        return 'road.local';
      case MapEnum.MapFeatures.LineTransit:
        return 'transit.line';
      case MapEnum.MapFeatures.AirportStation:
        return 'transit.station.airport';
      case MapEnum.MapFeatures.BusStation:
        return 'transit.station.bus';
      case MapEnum.MapFeatures.RailwayStation:
        return 'transit.station.rail';
      case MapEnum.MapFeatures.Water:
        return 'water';
    }
  }

  private getMapFeatures(features: MapEnum.MapFeatures[]) {
    const styles = [];

    for (const feature of features) {
      styles.push(this.getStyleObj(this.getFeatureText(feature)));
    }
    return styles;
  }

  private getStyleObj(feature: string) {
    return {
      featureType: feature,
      elementType: 'labels',
      stylers: [{ visibility: 'off' }],
    };
  }

  private getLatLngObject(latlng: google.maps.LatLng) {
    const obj = new LatLng();
    obj.Latitude = latlng.lat();
    obj.Longitude = latlng.lng();
    return obj;
  }

  private setDisplay(
    map: google.maps.Map,
    place: google.maps.places.PlaceResult,
  ) {
    if (!map) return;
    const bounds = new this.google.maps.LatLngBounds();
    if (place.geometry?.viewport) {
      bounds.union(place.geometry.viewport);
    } else if (place.geometry?.location) {
      bounds.extend(place.geometry.location);
    }
    map.fitBounds(bounds);
  }

  private getBoundPropertyObject(bounds: google.maps.LatLngBounds): MapBound {
    const boundProperties = new MapBound();
    boundProperties.Center = bounds.getCenter();
    boundProperties.SouthWest = this.getLatLngObject(bounds.getSouthWest());
    boundProperties.NorthEast = this.getLatLngObject(bounds.getNorthEast());
    return boundProperties;
  }

  async GetMapTypeId(mapType: MapEnum.MapType) {
    await this.googleMapsLoaded;
    switch (mapType) {
      case MapEnum.MapType.Hybrid:
        return this.google.maps.MapTypeId.HYBRID;
      case MapEnum.MapType.Roadmap:
        return this.google.maps.MapTypeId.ROADMAP;
      case MapEnum.MapType.Satellite:
        return this.google.maps.MapTypeId.SATELLITE;
      case MapEnum.MapType.Terrain:
        return this.google.maps.MapTypeId.TERRAIN;
      default:
        return this.google.maps.MapTypeId.ROADMAP;
    }
  }

  async GetLatLng(latitude: number, longitude: number) {
    await this.googleMapsLoaded;

    return new this.google.maps.LatLng(latitude, longitude);
  }

  async CreateMap(mapOptions: MapOptions, setCenter = false) {
    const mapOpts = await this.generateMapOptionsForGoogle(mapOptions);
    mapOpts.styles = this.getMapFeatures(mapOptions.FeaturesToHide);
    mapOpts.mapTypeId = this.google.maps.MapTypeId.HYBRID;
    const map = new this.google.maps.Map(
      document.getElementById(mapOpts.mapId ?? '') as HTMLElement,
      mapOpts,
    );
    if (setCenter) {
      this.SetCenter(map, mapOptions.CenterLat, mapOptions.CenterLng);
    }
    this.TiltMap(map);
    return map;
  }

  TiltMap(map: google.maps.Map, degree = 0): void {
    map.setTilt(degree);
  }

  OnMapZoomChange(
    map: google.maps.Map,
    callback: (boundProperties: MapBound) => unknown,
  ) {
    this.google.maps.event.addListener(map, 'zoom_changed', () => {
      const bounds = map.getBounds();
      if (bounds) {
        const boundProps = this.getBoundPropertyObject(bounds);
        if (callback) {
          this.zone.run(() => callback(boundProps));
        }
      }
    });
  }

  async AddController(
    map: google.maps.Map,
    controllerId: string,
    position: MapEnum.GoogleMapControlPosition,
  ) {
    const googlePosition = await this.getPositionValue(position);
    const control = document.getElementById(controllerId) as HTMLElement;
    map.controls[googlePosition].push(control);
    return map;
  }

  async AddSearchBox(
    map: google.maps.Map,
    searchBoxId: string,
    position: MapEnum.GoogleMapControlPosition,
    addController = true,
    onPlaceChanged: (place: google.maps.places.PlaceResult) => unknown,
  ) {
    const input = document.getElementById(searchBoxId);
    if (addController) {
      await this.AddController(map, searchBoxId, position);
    }
    const autocompleteOptions = {};
    const autocomplete = new this.google.maps.places.Autocomplete(
      input as HTMLInputElement,
      autocompleteOptions,
    );
    autocomplete.bindTo('bounds', map);

    this.google.maps.event.addListener(autocomplete, 'place_changed', () => {
      const place = autocomplete.getPlace();
      this.setDisplay(map, place);
      if (onPlaceChanged) this.zone.run(() => onPlaceChanged(place));
    });
  }

  ClearSingleMarker(marker?: google.maps.marker.AdvancedMarkerElement) {
    if (marker) {
      marker.map = null;
    }
  }

  StopMapMovement(
    map: google.maps.Map,
    marker: google.maps.marker.AdvancedMarkerElement,
  ) {
    marker.addListener('dragstart', () => {
      map.setOptions({ draggable: false });
    });
    marker.addListener('dragend', () => {
      map.setOptions({ draggable: true });
    });
  }

  async PlaceMarker(
    map: google.maps.Map,
    latitude: number,
    longitude: number,
    isDraggable = false,
    icon?: string,
  ) {
    let markerIcon;
    if (icon) {
      markerIcon = document.createElement('img');
      markerIcon.src = icon;
    } else {
      const pin = new this.google.maps.marker.PinElement({
        scale: 1,
      });
      markerIcon = pin.element;
    }

    const marker = new this.google.maps.marker.AdvancedMarkerElement({
      position: await this.GetLatLng(latitude, longitude),
      map: map,
      gmpDraggable: isDraggable,
      content: markerIcon,
    });
    this.StopMapMovement(map, marker);
    return marker;
  }

  async OnMapClick(
    map: google.maps.Map,
    callback: (latlng: LatLng) => unknown,
  ) {
    await this.googleMapsLoaded;
    this.google.maps.event.addListener(
      map,
      'click',
      (event: google.maps.MapMouseEvent & { latLng: google.maps.LatLng }) => {
        this.zone.run(() => callback(this.getLatLngObject(event.latLng)));
      },
    );
  }

  SetCenter(map: google.maps.Map, latitude: number, longitude: number) {
    setTimeout(async () => {
      map.setCenter(await this.GetLatLng(latitude, longitude));
    }, 1000);
  }

  OnMapViewPortChangedOnce(
    map: google.maps.Map,
    callback: (boundProperties: MapBound) => unknown,
  ): void {
    this.google.maps.event.addListenerOnce(map, 'idle', () => {
      const bounds = map.getBounds();
      if (bounds) {
        const boundProps = this.getBoundPropertyObject(bounds);
        if (callback) this.zone.run(() => callback(boundProps));
      }
    });
  }

  GetMapZoomLevel(map: google.maps.Map): number {
    return map.getZoom() ?? 0;
  }

  SetMapZoomLevel(map: google.maps.Map, zoomLevel: number): google.maps.Map {
    map.setZoom(zoomLevel);
    return map;
  }

  async SearchAddress(
    address: string,
  ): Promise<google.maps.GeocoderResult | null> {
    await this.googleMapsLoaded; // Ensure Google Maps API is loaded
    const geocoder = new this.google.maps.Geocoder();
    return new Promise((resolve) => {
      geocoder.geocode(
        { address },
        (
          results: google.maps.GeocoderResult[] | null,
          status: google.maps.GeocoderStatus,
        ) => {
          if (
            status === this.google.maps.GeocoderStatus.OK &&
            results &&
            results.length > 0
          ) {
            const place = results?.[0];
            resolve(place); // Return the first result
          } else {
            // this.notificationService.error(
            //   'Address not found',
            //   `Geocoder status: ${status}`,
            // );
            resolve(null);
          }
        },
      );
    });
  }
}
