import { Injectable } from '@angular/core';
import { Observable, map, forkJoin } from 'rxjs';

import {
  TransactionControllerService,
  TransactionDTO,
  ApiResponseListTransactionDTO,
  OrderResponseDTO,
  OrderControllerService,
} from '../../api-client';

import { UserService } from './user.service';

// Interfaces
export interface DocumentInfo {
  filePath: string;
  title: string;
  description?: string;
  documentType?: string;
  productName?: string;
  expiryDate?: string;
  issueDate?: string;
  finalPrice?: number;
  transactionId?: number;
  transactionDate?: string;
  referenceId?: string;
}

export interface TransactionWithOrder extends TransactionDTO {
  orderDetails?: OrderResponseDTO;
  documents?: DocumentInfo[];
  hasDocuments?: boolean;
}

@Injectable({
  providedIn: 'root',
})
export class TransactionService {
  constructor(
    private transactionController: TransactionControllerService,
    private orderController: OrderControllerService,
    private userService: UserService,
  ) {}

  /** Get all transactions for current user */
  getAllTransactions(
    type: 'USER' | 'COMPANY',
    id?: number,
  ): Observable<TransactionDTO[]> {
    const entityId = id ?? 0;
    return this.transactionController
      .getTransactionsByTypeAndId(type, entityId)
      .pipe(map((res: ApiResponseListTransactionDTO) => res.data || []));
  }

  /** Get transaction by ID */
  getTransactionById(id: number): Observable<TransactionDTO> {
    return this.transactionController
      .getTransactionById(id)
      .pipe(map((res) => res.data || ({} as TransactionDTO)));
  }

  /** Get order by transaction ID */
  getOrderByTransactionId(id: number): Observable<OrderResponseDTO> {
    return this.orderController
      .getOrderByTransactionId(id)
      .pipe(map((res) => res.data || ({} as OrderResponseDTO)));
  }

  /** Get transaction with its order and document details */
  getTransactionWithOrder(id: number): Observable<TransactionWithOrder> {
    return forkJoin({
      transaction: this.getTransactionById(id),
      order: this.getOrderByTransactionId(id),
    }).pipe(
      map(({ transaction, order }) => {
        const documents: DocumentInfo[] = (order?.items || []).flatMap(
          (item) =>
            item.document
              ? [
                  {
                    filePath: item.document.filePath || '',
                    title:
                      item.document.title || item.productName || 'Document',
                    description: item.document.description,
                    productName: item.productName,
                  },
                ]
              : [],
        );

        return {
          ...transaction,
          orderDetails: order,
          documents,
          hasDocuments: documents.length > 0,
        };
      }),
    );
  }

  /** Download a document */
  downloadDocument(url: string, fileName = 'document.pdf'): void {
    if (!url) return console.error('No document URL provided');

    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    link.target = '_blank';
    link.rel = 'noopener noreferrer';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  /** Download all documents */
  downloadAllDocuments(docs: DocumentInfo[], transactionId: number): void {
    if (!docs?.length) return console.error('No documents to download');

    docs.forEach((doc, index) => {
      setTimeout(() => {
        const fileName = `${transactionId}_${doc.productName || 'document'}_${index + 1}.pdf`;
        this.downloadDocument(doc.filePath, fileName);
      }, index * 500); // space downloads to avoid browser blocking
    });
  }

  /** View a document in a new tab */
  viewDocument(url: string): void {
    if (!url) return console.error('No document URL provided');
    window.open(url, '_blank', 'noopener,noreferrer');
  }

  /** View all documents */
  viewAllDocuments(docs: DocumentInfo[]): void {
    if (!docs?.length) return console.error('No documents to view');

    docs.forEach((doc, index) => {
      setTimeout(() => this.viewDocument(doc.filePath), index * 200);
    });
  }
}
