import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import {
  ApiResponseCompanySpecialPriceResponseDTO,
  ApiResponseDocumentPriceResponseDTO,
  ApiResponsePageCompanySpecialPriceResponseDTO,
  ApiResponsePageDocumentPriceResponseDTO,
  CompanySpecialPriceControllerService,
  CompanySpecialPriceRequestDTO,
  DocumentPriceControllerService,
  DocumentPriceRequestDTO,
} from '../../api-client';

@Injectable({
  providedIn: 'root',
})
export class PricingService {
  constructor(
    private documentPriceControllerService: DocumentPriceControllerService,
    private companySpecialPriceControllerService: CompanySpecialPriceControllerService,
  ) {}

  public getAllDocumentPrices(): Observable<ApiResponsePageDocumentPriceResponseDTO> {
    return this.documentPriceControllerService.getDocumentPrices(
      undefined,
      undefined,
      undefined,
      undefined,
      50,
      undefined,
    );
  }

  public createNewDocumentPrice(
    documentPriceRequestDTO: DocumentPriceRequestDTO,
  ): Observable<ApiResponseDocumentPriceResponseDTO> {
    return this.documentPriceControllerService.createDocumentPrice(
      documentPriceRequestDTO,
    );
  }

  public updateDocumentPrice(
    id: number,
    documentPriceRequestDTO: DocumentPriceRequestDTO,
  ): Observable<ApiResponseDocumentPriceResponseDTO> {
    return this.documentPriceControllerService.updateDocumentPrice(
      id,
      documentPriceRequestDTO,
    );
  }

  public getAllCompanySpecialDocumentPrices(
    companyId: number,
  ): Observable<ApiResponsePageCompanySpecialPriceResponseDTO> {
    return this.companySpecialPriceControllerService.getCompanySpecialPrices(
      undefined,
      companyId,
      undefined,
      undefined,
      50,
      undefined,
    );
  }

  public updateCompanySpecialDocumentPrice(
    id: number,
    companySpecialPriceRequestDTO: CompanySpecialPriceRequestDTO,
  ): Observable<ApiResponseCompanySpecialPriceResponseDTO> {
    return this.companySpecialPriceControllerService.updateCompanySpecialPrice(
      id,
      companySpecialPriceRequestDTO,
    );
  }
}
